# Maven 依赖管理最佳实践

## 1. 版本属性管理

### 统一命名规范
```xml
<!-- 推荐：使用连字符分隔 -->
<spring-boot.version>3.2.4</spring-boot.version>
<mybatis-plus.version>3.5.9</mybatis-plus.version>

<!-- 避免：混合使用点号和连字符 -->
<spring.boot.version>3.2.4</spring.boot.version>
<mybatis-plus.version>3.5.9</mybatis-plus.version>
```

### 版本属性分组
```xml
<properties>
    <!-- 基础环境 -->
    <java.version>17</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    
    <!-- Spring 生态系统 -->
    <spring-boot.version>3.2.4</spring-boot.version>
    <spring-cloud.version>2023.0.1</spring-cloud.version>
    
    <!-- 数据库相关 -->
    <mybatis-plus.version>3.5.9</mybatis-plus.version>
    <druid.version>1.2.25</druid.version>
    
    <!-- 工具库 -->
    <hutool.version>5.8.37</hutool.version>
    <fastjson2.version>2.0.55</fastjson2.version>
</properties>
```

## 2. DependencyManagement 组织

### 按功能分组
```xml
<dependencyManagement>
    <dependencies>
        <!-- ========================================= -->
        <!-- Spring 生态系统 BOM 依赖管理 -->
        <!-- ========================================= -->
        
        <!-- SpringBoot 依赖配置 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        
        <!-- ========================================= -->
        <!-- 数据库相关依赖 -->
        <!-- ========================================= -->
        
        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

## 3. 安全版本管理

### 定期更新策略
1. **每月检查**: 检查关键依赖的安全更新
2. **漏洞扫描**: 使用工具如 OWASP Dependency Check
3. **测试验证**: 在测试环境验证新版本兼容性

### 安全版本示例
```xml
<!-- 解决已知安全漏洞的版本 -->
<netty.version>4.1.118.Final</netty.version>
<tomcat.version>10.1.33</tomcat.version>
<guava.version>33.4.0-jre</guava.version>
```

## 4. 避免常见问题

### 避免重复声明
```xml
<!-- 错误：重复声明同一依赖 -->
<dependency>
    <groupId>jakarta.servlet</groupId>
    <artifactId>jakarta.servlet-api</artifactId>
    <version>${jakarta.servlet-api.version}</version>
</dependency>
<!-- ... 其他依赖 ... -->
<dependency>
    <groupId>jakarta.servlet</groupId>
    <artifactId>jakarta.servlet-api</artifactId>
    <version>${jakarta.servlet-api.version}</version>
</dependency>
```

### 合理使用 Exclusions
```xml
<!-- 推荐：在根 pom 中统一管理版本 -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring-web.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>

<!-- 避免：在每个子模块中重复排除和引入 -->
<dependency>
    <groupId>some-library</groupId>
    <artifactId>some-artifact</artifactId>
    <exclusions>
        <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

## 5. 版本兼容性检查

### Spring Boot 3.x 兼容性
- Java 17+ 必需
- Jakarta EE 9+ (不是 javax)
- Spring Framework 6.x
- Spring Security 6.x

### 检查清单
- [ ] Java 版本兼容性
- [ ] Spring 生态系统版本对应关系
- [ ] 第三方库 Spring Boot 3.x 支持
- [ ] API 变更和迁移指南

## 6. 自动化工具

### Maven 插件
```xml
<!-- 版本检查插件 -->
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>versions-maven-plugin</artifactId>
    <version>2.16.2</version>
</plugin>

<!-- 安全扫描插件 -->
<plugin>
    <groupId>org.owasp</groupId>
    <artifactId>dependency-check-maven</artifactId>
    <version>9.0.7</version>
</plugin>
```

### 常用命令
```bash
# 检查可更新的依赖
mvn versions:display-dependency-updates

# 检查可更新的插件
mvn versions:display-plugin-updates

# 安全漏洞扫描
mvn org.owasp:dependency-check-maven:check
```

## 7. 团队协作规范

### Code Review 检查点
- [ ] 新增依赖是否必要
- [ ] 版本选择是否合理
- [ ] 是否存在重复声明
- [ ] 是否遵循命名规范
- [ ] 是否有安全漏洞

### 文档维护
- 维护依赖变更日志
- 记录重要的版本升级原因
- 提供迁移指南和注意事项
