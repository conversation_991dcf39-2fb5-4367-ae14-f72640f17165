# IDEA 运行配置指南

## 为每个服务创建 IDEA 运行配置

### 1. SSC 服务配置

**Main class:** `com.deloitte.dhr.ssc.DHRSscApplication`

**VM options:**
```
-Dotel.javaagent.enabled=false
-Dotel.sdk.disabled=true
```

**Active profiles:**
```
test
```

**Environment variables:**
```
config_nacos_serveraddr=localhost
config_nacos_port=8848
config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
config_profile=test
config_server_port=9027
```

**Working directory:**
```
$MODULE_WORKING_DIR$
```

### 2. Performance 服务配置

**Main class:** `com.deloitte.dhr.performance.DHRPerformanceApplication`

**VM options:**
```
-Dotel.javaagent.enabled=false
-Dotel.sdk.disabled=true
```

**Active profiles:**
```
test
```

**Environment variables:**
```
config_nacos_serveraddr=localhost
config_nacos_port=8848
config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
config_profile=test
config_server_port=9028
```

### 3. Talent 服务配置

**Main class:** `com.deloitte.dhr.talent.DHRTalentApplication`

**VM options:**
```
-Dotel.javaagent.enabled=false
-Dotel.sdk.disabled=true
```

**Active profiles:**
```
test
```

**Environment variables:**
```
config_nacos_serveraddr=localhost
config_nacos_port=8848
config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
config_profile=test
config_server_port=9029
```

### 4. MDA 服务配置

**Main class:** `com.deloitte.dhr.mda.DHRMdaApplication`

**VM options:**
```
-Dotel.javaagent.enabled=false
-Dotel.sdk.disabled=true
```

**Active profiles:**
```
test
```

**Environment variables:**
```
config_nacos_serveraddr=localhost
config_nacos_port=8848
config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
config_profile=test
config_server_port=9024
```

### 5. Performance New 服务配置

**Main class:** `com.deloitte.dhr.performance.DHRPerformanceNewApplication`

**VM options:**
```
-Dotel.javaagent.enabled=false
-Dotel.sdk.disabled=true
```

**Active profiles:**
```
test
```

**Environment variables:**
```
config_nacos_serveraddr=localhost
config_nacos_port=8848
config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
config_profile=test
config_server_port=9049
```

## 常见问题解决

### 1. ExcelParserLogMapper 找不到
- 确保启动类已添加 `com.deloitte.dhr.excel.parser` 到 scanBasePackages
- 检查 Maven 依赖是否正确

### 2. Nacos 配置格式错误
- 确保使用 `spring.config.activate.on-profile` 而不是 `spring.profiles`
- 所有 Nacos 配置已更新为正确格式

### 3. OpenTelemetry 冲突
- 添加 JVM 参数禁用 OpenTelemetry
- 检查 MySQL 连接器版本兼容性

### 4. 数据库连接问题
- 确保数据库服务器可访问
- 检查数据库连接配置

## 启动顺序建议

1. 先启动 Nacos 服务
2. 启动 MDA 服务（基础数据服务）
3. 启动其他业务服务（SSC、Performance、Talent 等）
