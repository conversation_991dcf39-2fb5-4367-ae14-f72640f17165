package com.deloitte.dhr.report.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.openqa.selenium.PageLoadStrategy;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Selenium 工具类
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Component
public class SeleniumUtil {
    /**
     * 驱动driver
     */
    private static final AtomicReference<RemoteWebDriver> driver = new AtomicReference<>();

    /**
     * 服务地址
     */
    public static String serverUrl;

    /**
     * 是否心跳检查
     */
    private static Boolean isHeartbeatKeep;

    /**
     * 心跳检查周期
     */
    private static Integer heartbeatTime = 240000;

    private final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("selenium-pool-%d").build();

    /**
     * 线程池
     */
    private static ExecutorService cachedThreadPool = new ThreadPoolExecutor(1,5,300, TimeUnit.SECONDS,new ArrayBlockingQueue<>(10));

    public static void setServerUrl(String serverUrl) {
        SeleniumUtil.serverUrl = serverUrl;
    }

    /**
     * 获取 单例 RemoteWebDriver
     * @return
     */
    public static RemoteWebDriver getSingletonRemoteWebDriver() {
        RemoteWebDriver result = driver.get();
        if (result == null) {
            synchronized (SeleniumUtil.class) {
                result = driver.get();
                if (result == null) {
                    result = createNewDriver();
                    driver.set(result);
                    heartbeatKeep();
                }
            }
        }
        return result;
    }
    private static RemoteWebDriver createNewDriver() {
        ChromeOptions browserOptions = new ChromeOptions();
        browserOptions.addArguments("--headless");
        browserOptions.setPageLoadStrategy(PageLoadStrategy.NORMAL);
        browserOptions.setExperimentalOption("detach", true);

        try {
            return new RemoteWebDriver(new URL(serverUrl), new DesiredCapabilities(browserOptions));
        } catch (MalformedURLException e) {
            throw new RuntimeException("Failed to create RemoteWebDriver", e);
        }
    }

    /**
     * 保持心跳检查
     */
    private static void heartbeatKeep() {
        isHeartbeatKeep = true;
        cachedThreadPool.execute(new Runnable() {
            @Override
            public void run() {
                while (isHeartbeatKeep) {
                    RemoteWebDriver currentDriver = getSingletonRemoteWebDriver();
                    try {
                        if (currentDriver != null) {
                            currentDriver.getCurrentUrl();
                        }
                        Thread.sleep(heartbeatTime);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }

            }
        });
    }

    /**
     * 获取新的 RemoteWebDriver
     *
     * @return
     */
    public static RemoteWebDriver getNewRemoteWebDriver() {
        ChromeOptions browserOptions = new ChromeOptions();
        browserOptions.addArguments("--headless");
        browserOptions.setPageLoadStrategy(PageLoadStrategy.NORMAL);
        // Keeping browser open
        browserOptions.setExperimentalOption("detach",true);
        DesiredCapabilities dc = new DesiredCapabilities(browserOptions);
        try {
            return new RemoteWebDriver(new URL(serverUrl), dc);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }

    public static void setDriver(RemoteWebDriver newDriver) {
        synchronized (SeleniumUtil.class) {
            driver.set(newDriver);
        }
    }

    /**
     * 手动退出
     *
     * @return
     */
    public static Boolean quit() {
        synchronized (SeleniumUtil.class) {
            RemoteWebDriver currentDriver = driver.get();
            if (currentDriver != null) {
                currentDriver.quit();
                driver.set(null);
            }
            isHeartbeatKeep = false;
            return true;
        }
    }

    public static void setIsHeartbeatKeep(Boolean isHeartbeatKeep) {
        SeleniumUtil.isHeartbeatKeep = isHeartbeatKeep;
    }

    public static void setHeartbeatTime(Integer heartbeatTime) {
        SeleniumUtil.heartbeatTime = heartbeatTime;
    }
}