package com.deloitte.dhr.resubmit.Strategy;

import com.deloitte.dhr.common.base.utils.MD5Util;
import com.deloitte.dhr.resubmit.annotation.Resubmit;
import com.deloitte.dhr.resubmit.exception.RedisLockException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/***
 * @Author: yinyu
 * @Date: 23/03/2023 10:04
 * @description:
 */
@Slf4j
@Component
public class DefaultResubmitStrategy implements ResubmitStrategyHandler {

    @Resource
    private RedissonClient redissonClient;

    /**
     * key前缀, 用于区分不同业务
     */
    @Value("${redisGroup.resubmit:resubmit}")
    private String prefix;

    @Override
    public Object handle(ProceedingJoinPoint joinPoint, Resubmit resubmit) throws Throwable {
        final String methodKey = buildMethodKey(joinPoint, resubmit);
        final RLock lock = redissonClient.getLock(methodKey);
        boolean lockAcquired = false;

        try {
            // 尝试获取锁（0秒等待，立即返回结果）
            lockAcquired = lock.tryLock(0, resubmit.leaseTime(), TimeUnit.SECONDS);  //NOSONAR

            if (!lockAcquired) {
                log.warn("Resubmit blocked for key: {}", methodKey);
                throw new RedisLockException(LOCK_FAIL_MESSAGE);
            }

            // 执行目标方法
            return joinPoint.proceed();
        } finally {
            // 确保只释放当前线程持有的锁
            releaseLockSafely(lock, lockAcquired, methodKey);
        }
    }

    // 安全释放锁的专用方法
    private void releaseLockSafely(RLock lock, boolean lockAcquired, String methodKey) {
        if (lockAcquired) {
            try {
                // 双重检查确保当前线程持有锁
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.debug("Lock released for key: {}", methodKey);
                } else {
                    log.warn("Current thread does not hold the lock for key: {}", methodKey);
                }
            } catch (IllegalMonitorStateException ex) {
                log.warn("Lock already released for key: {}", methodKey, ex);
            } catch (Exception ex) {
                log.error("Failed to release lock for key: {}", methodKey, ex);
            }
        }
    }

    // 提取键生成逻辑到独立方法
    private String buildMethodKey(ProceedingJoinPoint joinPoint, Resubmit resubmit) {
        String rawKey = getRedisKey(joinPoint, resubmit.keys());
        String md5Key = MD5Util.encrypt(rawKey);
        return String.format("%s:%s", prefix, md5Key);
    }

    private String getRedisKey(ProceedingJoinPoint joinPoint,String[] keys){
        //得到被切面修饰的方法的参数列表
        Object[] args = joinPoint.getArgs();
        //得到被代理的方法
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        StringBuilder stringBuilder=new StringBuilder();
        //获取被拦截方法参数名列表(使用Spring支持类库)
        StandardReflectionParameterNameDiscoverer parameterNameDiscoverer = new StandardReflectionParameterNameDiscoverer();
        String[] paraNameArr = parameterNameDiscoverer.getParameterNames(method);
        if (paraNameArr == null) {
            throw new IllegalArgumentException("无法获取方法参数名称，请确保编译时保留调试信息。方法：" + method.getDeclaringClass().getName() + "." + method.getName());
        }
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        //把方法参数放入SPEL上下文中
        for(int i=0;i<paraNameArr.length;i++){
            context.setVariable(paraNameArr[i], args[i]);
        }
        for (String key:keys){
            stringBuilder.append(parser.parseExpression(key).getValue(context,String.class));
        }
        return stringBuilder.toString();
    }

}
