spring:
  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

  cloud:
    gateway:
      #      discovery:
      #        locator:
      #          # 开启项目名称映射功能，即通过服务名访问项目，不需要写明具体IP地址
      #          enabled: true
      #          # 请求路由全部转小写字母，如 Aa/Bb 会被转为 aa/bb
      #          lower-case-service-id: true
      globalcors:
        corsConfigurations:
          '[/**]': # 匹配所有请求
            allow-credentials: true
            allowedOriginPatterns: ["pscdev.deloitte.com.cn"] #跨域处理 允许所有的域
            allowedHeader: ["Page-Code", "Content-Type", "Authorization"]
            allowedMethods: # 支持的方法
              - GET
              - POST
              - PUT
              - DELETE
              - PATCH
      routes:
        #微服务uaa-service
        - id: dhr-personnel-oauth-service
          uri: lb://dhr-personnel-oauth-service
          predicates:
            - Path=/dhr-personnel-oauth-service/**
        #          filters:
        #            - StripPrefix=1
        #微服务order-service
        - id: order-service
          uri: lb://order-service
          predicates:
            - Path=/order-service/**
          filters:
            - StripPrefix=1

        - id: jiami-service
          uri: lb://jiami-service
          predicates:
            - Path=/jiami-service/**
        #          filters:
        #            - StripPrefix=1


        - id: action-service
          uri: lb://action-service
          predicates:
            - Path=/action-service/**
          filters:
            - StripPrefix=1

        #微服务basic-service
        - id: dhr-personnel-basic-service
          uri: lb://dhr-personnel-basic-service
          predicates:
            - Path=/dhr-personnel-basic-service/**
          filters:
            - StripPrefix=1

            #微服务tools-service
        - id: dhr-personnel-tools-service
          uri: lb://dhr-personnel-tools-service
          predicates:
            - Path=/dhr-personnel-tools-service/**
          filters:
            - StripPrefix=1

        #微服务talent-assessment-service
        - id: dhr-personnel-assessment-service
          uri: lb://dhr-personnel-assessment-service
          predicates:
            - Path=/dhr-personnel-assessment-service/**
          filters:
            - StripPrefix=1

        #微服务talent-management-service
        - id: dhr-personnel-management-service
          uri: lb://dhr-personnel-management-service
          predicates:
            - Path=/dhr-personnel-management-service/**
          filters:
            - StripPrefix=1

        #微服务questionnaire-service
        - id: dhr-personnel-questionnaire-service
          uri: lb://dhr-personnel-questionnaire-service
          predicates:
            - Path=/dhr-personnel-questionnaire-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-basic-service
        - id: dhr-basic-service
          uri: lb://dhr-basic-service
          predicates:
            - Path=/dhr-basic-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-admin-service
        - id: dhr-admin-service
          uri: lb://dhr-admin-service
          predicates:
            - Path=/dhr-admin-service/**
          filters:
            - StripPrefix=1
        #微服务dhr-oauth-service
        - id: dhr-oauth-service
          uri: lb://dhr-oauth-service
          predicates:
            - Path=/dhr-oauth-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-utility-service
        - id: dhr-utility-service
          uri: lb://dhr-utility-service
          predicates:
            - Path=/dhr-utility-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-basic-service
        - id: dhr-performance-service
          uri: lb://dhr-performance-service
          predicates:
            - Path=/dhr-performance-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-bpm-service
        - id: dhr-bpm-service
          uri: lb://dhr-bpm-service
          predicates:
            - Path=/dhr-bpm-service/**
          filters:
            - StripPrefix=1
        - id: dhr-workflow-service
          uri: lb://dhr-workflow-service
          predicates:
            - Path=/dhr-workflow-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-okr-provider-service
        - id: dhr-okr-provider-service
          uri: lb://dhr-okr-provider-service
          predicates:
            - Path=/dhr-okr-provider-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-utility-service
        - id: dhr-utility-service
          uri: lb://dhr-utility-service
          predicates:
            - Path=/dhr-utility-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-tign-service
        - id: dhr-tign-service
          uri: lb://dhr-tign-service
          predicates:
            - Path=/dhr-tign-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-questionnaire-service
        - id: dhr-questionnaire-service
          uri: lb://dhr-questionnaire-service
          predicates:
            - Path=/dhr-questionnaire-service/**
          filters:
            - StripPrefix=1

        - id: dhr-collection-service
          uri: lb://dhr-collection-service
          predicates:
            - Path=/dhr-collection-service/**
          filters:
            - StripPrefix=1
        #微服务dhr-mda-service
        - id: dhr-mda-service
          uri: lb://dhr-mda-service
          predicates:
            - Path=/dhr-mda-service/**
          filters:
            - StripPrefix=1
        #微服务dhr-talent-service
        - id: dhr-talent-service
          uri: lb://dhr-talent-service
          predicates:
            - Path=/dhr-talent-service/**
          filters:
            - StripPrefix=1
        #微服务dhr-ssc-service
        - id: dhr-ssc-service
          uri: lb://dhr-ssc-service
          predicates:
            - Path=/dhr-ssc-service/**
          filters:
            - StripPrefix=1
        #微服务dhr-ssc-service
        - id: dhr-perf-service
          uri: lb://dhr-perf-service
          predicates:
            - Path=/dhr-perf-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-ai-duo-service
        - id: dhr-ai-duo-service
          uri: lb://dhr-ai-duo-service
          predicates:
            - Path=/dhr-ai-duo-service/**
          filters:
            - StripPrefix=1
             
        #微服务dhr-ai-assistant-service
        - id: dhr-ai-assistant-service
          uri: lb://dhr-ai-assistant-service
          predicates:
            - Path=/dhr-ai-assistant-service/**
          filters:
            - StripPrefix=1  

        #微服务dhr-perf-new-service
        - id: dhr-perf-new-service
          uri: lb://dhr-perf-new-service
          predicates:
            - Path=/dhr-perf-new-service/**
          filters:
            - StripPrefix=1

        #微服务dhr-ai-service
        - id: dhr-ai-service
          uri: lb://dhr-ai-service
          predicates:
            - Path=/dhr-ai-service/**
          filters:
            - StripPrefix=1                      
server:
  port: 9110

elk:
  logstash:
    dest: ***********:5000

excludePathAuth:
  urls: "/dhr-personnel-questionnaire-service/questionnaire/employeeSearch/loadQrCode,/dhr-personnel-questionnaire-service/questionnaire/qs-qrcode-answer/saveQuestionAnSwer,/dhr-personnel-questionnaire-service/questionnaire/qs-qrcode-answer/readQuestionAnSwer,/dhr-personnel-assessment-service/assess/trasanction-process-approve/getAnswerSheet,/dhr-basic-service/tign,/dhr-utility-service,/dhr-oauth-service/logout,/dhr-oauth-service/sso,/dhr-oauth-service/dhrSSO,/sms/jenkinsSendMessage,/dhr-basic-service/emp/job/verify/parseParams,/dhr-admin-service/,
  /dhr-basic-service/chb/getDataFiles,/dhr-basic-service/chb/handleDataFiles,/dhr-basic-service/email/rcSendEmail,
/dhr-basic-service/message,/dhr-basic-service/hr/bpm/callback,/dhr-talent-service/DhrEaActivityInfo/bpm/callback,/dhr-basic-service/hr/bpm/push/sap,/dhr-download-service/upload/ufs/download,/dhr-basic-service/entryguide/ocr/getIdCardPic,/dhr-basic-service/entryguide/ocr/getBankCardPic,/dhr-basic-service/accesslog/saveLog,/druid,/actuator,/dhr-questionnaire-service/paper/preReviewForAnyOne,/dhr-questionnaire-service/paper/paperLanguage,/dhr-questionnaire-service/paper/answer/getPaperById,/dhr-questionnaire-service/paper/selectLanguage,/dhr-questionnaire-service/paper/paperExtend/queryPaperExtend,/dhr-questionnaire-service/paper/answer/view,/dhr-questionnaire-service/paper/answer/history,/dhr-questionnaire-service/paper/answer/detail,/dhr-questionnaire-service/paper/answer/answerData,/dhr-questionnaire-service/paper/answer/queryLastAnswerRecord,/dhr-questionnaire-service/paper/answer/queryPaperDetail,/dhr-questionnaire-service/answer/login/code,/dhr-questionnaire-service/answer/login,
/dhr-collection-service/collection/saveLog,/dhr-collection-service/collection/saveAction,/downloadModel,/dhr-questionnaire-service/paper/share/paperLinkInfo,/dhr-questionnaire-service/paper/preReviewForAnyOne,/dhr-questionnaire-service/paper/paperLanguage,/dhr-questionnaire-service/paper/answer/getPaperById,
/dhr-questionnaire-service/paper/selectLanguage,/dhr-questionnaire-service/paper/paperExtend/queryPaperExtend,/dhr-questionnaire-service/paper/answer/view,/dhr-questionnaire-service/paper/answer/history,
/dhr-questionnaire-service/paper/answer/detail,/dhr-questionnaire-service/paper/answer/answerData,/dhr-questionnaire-service/paper/answer/queryLastAnswerRecord,/dhr-questionnaire-service/paper/answer/queryPaperDetail,
/dhr-questionnaire-service/answer/login/code,/dhr-questionnaire-service/answer/login,/dhr-ai-duo-service/AiModel/coach/callback,/dhr-perf-new-service/DhrPerformanceProcess/callback"

#内部服务路径
internalService:
  urls: "/dhr-oauth-service,/dhr-bpm-service,/dhr-basic-service/extranet/getAuthCode,/dhr-basic-service/extranet/verifyCode,/dhr-basic-service/extranet/analysisToken,/dhr-basic-service/extranet/baseMethod,/dhr-tign-service/test,/dhr-basic-service/tign/tignUrl,/dhr-personnel-questionnaire-service/questionnaire/employeeSearch/loadQrCode,/dhr-personnel-questionnaire-service/dh2-link/questionnaire/questionnaire-qr-code,/dhr-personnel-questionnaire-service/questionnaire/qs-paper-setting/loadViewQrCode,upload/ufs/uploadOffer,/dhr-download-service/upload/ufs/downloadEmailFile,/dhr-download-service/upload/ufs/getDownloadUrlList,\
    /dhr-download-service/upload/ufs/uploadFile,/login/getMenu,/upload/ufs/uploadProve,/upload/ufs/getFileByteByFileId,/dhr-download-service/upload/ufs/addShare,\
    /printLog,/dhr-basic-service/sap/incentive/push,/dhr-basic-service/sap/positive/push,/dhr-basic-service/tableAurequest/redirection,/dhr-basic-service/emp/job/verify/page,\
    /dhr-basic-service/emp/resumes/print/setToken,/dhr-basic-service/entryguide/getPhone,/dhr-basic-service/entryguide/send,/dhr-basic-service/entryguide/vaild,\
    /dhr-basic-service/entryguide/qrCode/createQrcode,/dhr-basic-service/entryguide/qrcodeSend,/dhr-basic-service/salary/approval/page,\
    /dhr-basic-service/chb/getDataFiles,/dhr-basic-service/chb/handleDataFiles,\
    /dhr-basic-service/signature/preview/page,/dhr-basic-service/hr/bpm/callback,/dhr-talent-service/DhrEaActivityInfo/bpm/callback,/dhr-basic-service/hr/bpm/push/sap,/dhr-basic-service/hr/bpm/push/sap,/dhr-download-service/upload/ufs/download,/dhr-admin-service/,/dhr-basic-service/entryguide/ocr/getIdCardPic,/dhr-basic-service/entryguide/ocr/getBankCardPic,/dhr-basic-service/entryguide,/dhr-basic-service/droplist/common/extends,/dhr-basic-service/hr/process/getId,/druid,/actuator,/dhr-basic-service/email/sapBatchSend,/dhr-basic-service/message,/downloadModel,\
    /dhr-basic-service/message/saveMessage,/dhr-ssc-service/DhrMiTaskInfo/sapAdd,/dhr-ssc-service/DhrMiTaskInfo/sapUpd,/dhr-ssc-service/DhrMiInitiate/sapSave,/dhr-perf-service/DhrTgMyTarget/bpm/callback,/dhr-perf-service/DhrTcTargetCommunicate/bpm/callback,/dhr-perf-service/DhrPaPerfAppraisal/bpm/callback,/dhr-mda-service/sync/executeSyncData,\
    /dhr-ai-assistant-service,\
    /dhr-perf-service/DhrTgMyTarget/bpm/callback,\
    /dhr-perf-new-service/DhrTcTargetCommunicate/bpm/callback,/dhr-perf-new-service/DhrPaPerfAppraisal/bpm/callback,/dhr-perf-new-service/DhrPerformanceProcess/callback"
    
#暴露给第三方的服务
exposureServices:
  urls: "/k2/saveK2Status,/login/getUserInfo,/templateSignature/callback"
  secretKey: f7RIkx2luYfKRocpcu5L0T4qiLMjKpDDY9BHp7UYfqDTzEDk-ujCQvmqJXhR-HSuTX4g_hhP0yljOw25523txA

mobileAuth:
  urls: dhr-download-service,/sap/searchhelp,yxMoblieAuth/ssoys,yxMoblieAuth/sso,ysyxMoblieAuth/sso,/pt/create_qj,/pt/search,/info/getInfoTaitou,/report/resume,/report/resume/details,/dept/orgBaseInfo,/login/getUserInfo
  secretKey: f7RIkx2luYfKRocpcu5L0T4qiLMjKpDDY9BHp7UYfqDTzEDk-ujCQvmqJXhR-HSuTX4g_hhP0yljOw25523txA

projectGroup:
  rcurl: dhr-personnel-questionnaire-service
  basicurl: dhr-workflow-service,dhr-download-service,dhr-performance-service,dhr-oauth-service,dhr-basic-service,dhr-utility-service,dhr-questionnaire-service,dhr-collection-service,dhr-talent-service,dhr-ssc-service,dhr-perf-service,dhr-core-service,dhr-mda-service,dhr-ai-duo-service,dhr-ai-assistant-service,dhr-perf-new-service

SM4:
  enable: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
