spring:
  boot:
    admin:
      client:
        url:  lb://dhr-admin-service

  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: jdbc:mysql://************:3306/dhr_mda_test?characterEncoding=utf8&useUnicode=true&useSSL=false&serverTimezone=GMT%2B8
    username: dhr_mda_test
    password: LI@J!lZsgJ0u
    #    username: dhr_yx_basic_dev
    #    password: yx_basic_dev321
    driver-class-name: com.mysql.cj.jdbc.Driver


#分页
pagehelper:
  helper-dialect: mariadb
  reasonable: false
  support-methods-arguments: true
  params: 'count=countSql'


elk:
  logstash:
    dest: ***********:5000

sap:
  #*************
  jcoAsHost: MTAxLjEzMi43MS4xMDU=
  #jcoAsHost: MTAuMS4xMjUuMjg=
  jcoSysnr: 00
  jcoClient: 110
  #************
  jcoClientRouter: ''
  jcoUser: SFJfRUND
  #HR_ECC
  jcoPasswd: MTIzNDU2Nzg=
  #12345678
  jcoLang: ZH
  jcoPoolCapacity: 30
  jcoPeakLimit: 10
  jcoMsHost: ''
  jcoMsServ: ''
  jcoGroup: ''
  jcoR3name: ''


ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000


#不需要验证码效验
verifyCode:
  enable: false


feign:
  hystrix:
    enabled: true  #开启Feign的熔断功能
  client:
    config:
      default:
        connectTimeout: 10000  #连接超时时间10秒
        readTimeout: 20000     #读超时时间10秒
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 50000 #设置熔断时间50秒
management:
  endpoints:
    web:
      exposure:
        include: '*'

mybatis-plus:
  # 扫描 mapper.xml
  mapper-locations: classpath:mappers/**/*.xml  #也可以不配置，在代码中设置
  type-aliases-package: com.deloitte.dhr.mda.module.**.domain
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-enums-package: com.deloitte.dhr.mda.enums
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true


dhr:
  # 替换s3 资源文件链接路径
  fileUrl: http://172.16.4.93:9000/
  viewUrl: https://pscdev.deloitte.com.cn/dhr/dev/view/  
  swagger:
    enabled: true
    docket:
      basic:
        title: 主数据模块API
        base-package: com.deloitte.dhr.mda #需要扫描的包名
  auth:
    enabled: false