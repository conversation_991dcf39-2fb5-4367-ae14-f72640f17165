### web
server:
  servlet:
    context-path: ${path-prefix}/xxl-job-admin

### actuator
management:
  health:
    mail:
      enabled: false
  server:
    servlet:
      context-path: /actuator

### resources
spring:
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  resources:
    static-locations: classpath:/static/

  ### freemarker
  freemarker:
    templateLoaderPath: classpath:/templates/
    suffix: .ftl
    request-context-attribute: request
    charset: UTF-8
    settings:
      number_format: 0.##########


  ### xxl-job, datasource
  datasource:
    url: *********************************************************************************************************************
    username: dhr_mda_dev
    password: dhr_mda_dev321
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 10
      maximum-pool-size: 30
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 900000
      connection-timeout: 10000
      connection-test-query: SELECT 1
      validation-timeout: 1000

  ### xxl-job, email
  mail:
    host: mx1.deloittecn.iphmx.com
    port: 25
    username: <EMAIL>
    from: <EMAIL>
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false
            required: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory


mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml

### xxl-job, access token
xxl:
  job:
    accessToken: default_token
    ### xxl-job, i18n (default is zh_CN, and you can choose "zh_CN", "zh_TC" and "en")
    i18n: zh_CN
    ## xxl-job, triggerpool max size
    triggerpool.fast.max: 200
    triggerpool.slow.max: 100
    ### xxl-job, log retention days
    logretentiondays: 30

    # 把调度中心本身做为执行器
    admin:
      addresses: http://127.0.0.1:9040${path-prefix}/xxl-job-admin
    executor:
      appname: dhr-xxl-job-executor
      address: ''
      ip: ''
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
    # GLUE模式请求环境地址
    request:
      address:
        dev:
          mda: https://pscdev.deloitte.com.cn/dhr/dev/data-server
          performance: https://pscdev.deloitte.com.cn/dhr/dev/dhrPerfService
          talent: https://pscdev.deloitte.com.cn/dhr/dev/dhrTalentService
          ssc: https://pscdev.deloitte.com.cn/dhr/dev/dhrSscService
        test:
          mda: https://pscdev.deloitte.com.cn/dhr/test/data-server
          performance: https://pscdev.deloitte.com.cn/dhr/test/dhrPerfService
          talent: https://pscdev.deloitte.com.cn/dhr/test/dhrTalentService
          ssc: https://pscdev.deloitte.com.cn/dhr/test/dhrSscService
        demo:
          mda:
          performance:
          talent:
          ssc:
      accessToken: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsiYWN0aW9uIiwib3JkZXIiXSwiZXhwIjoxNjgwMTc1MzU2LCJ1c2VyX25hbWUiOiJ7XCJlbWFpbFwiOlwiMTczMjUzMDAxN0BxcS5jb21cIixcImVtcGxveWVlTnVtYmVyXCI6XCIwMDAwMDAwMlwiLFwiZnVsbG5hbWVcIjpcIueOi-WImlwiLFwiaWRcIjpcIjE2NFwiLFwibW9iaWxlXCI6XCIxMzI2MzM5Nzk5NFwiLFwib2FOYW1lXCI6XCIwMDAwMDAwMlwiLFwicGFzc3dvcmRcIjpcIiQyYSQxMCR0bjJMUlg1Szd6ZFhIdlR6N0ZRd1UuUkVXV2g3UjkvalZtdW5xNm1uV3BwamF4NmJhVkRadVwiLFwidXNlcm5hbWVcIjpcIjAwMDAwMDAyXCJ9IiwianRpIjoiNDExMTc1ZWUtNmE3OC00OGMxLWFhYTMtMTA2YTJjZDBlMmY0IiwiY2xpZW50X2lkIjoicmVzb3VyY2UxIiwic2NvcGUiOlsiUk9MRV9BRE1JTiIsIlJPTEVfVVNFUiIsIlJPTEVfQVBJIiwiYWxsIl19.1Nk3xR6fuw_TTESu3yEzry_JTFRxiS9NcUpfTjQUKYE

path-prefix: /dhr/test