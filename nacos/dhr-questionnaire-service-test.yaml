# Tomcat
server:
  port: 9205
# spring配置
spring:
  config:
    activate:
      on-profile: dev
  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms
    lettuce:
      pool:
        min-idle: 10
        max-idle: 80
        max-wait: -1
        max-active: 80
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
          # 主库数据源
          master:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: ***********************************************************************************************************************************************************************************
            username: dhr_question_dev
            password: dhr_question_dev321
          # 从库数据源
          #slave:
            #username: data_pre_ap_dev
            #password: As1ooslo12
            #url: ***********************************************************************************************************************************************************************************************************************************************
            #driver-class-name: com.mysql.cj.jdbc.Driver
      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
  data:
    mongodb:
      uri: *****************************************
      authenticationDatabase: admin
      database: survey_dev
      uuid-representation: standard
  messages:
    basename: i18n/messages,i18n-base/messages

#请求处理的超时时间
ribbon:
  ReadTimeout: 10000
  ConnectTimeout: 10000

# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
      min-request-size: 819200
      mime-types: text/xml,application/xml,application/json
    response:
      enabled: true

# seata配置
seata:
  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      ruoyi-system-group: default
  config:
    type: nacos
    nacos:
      serverAddr: 172.16.5.16:8848
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 172.16.5.16:8848
      namespace:

# mybatis配置
mybatis:
    # 搜索指定包别名
  typeAliasesPackage: com.deloitte.dhr.questionnaire.provider.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

jwt:
  #token密匙
  encryptJWTKey: U0JBUElKV1RkV2FuZzkyNjQ1NA==
  #token过期时间 单位：分
  accessTokenExpireTime: 60

com:
  deloitte:
    paper:
     sms:
      answer:
        templateCode: "SMS_122225006"
        signName: "阿里云短信测试专用"
    sms:
      templateCode: "SMS_122225006"
      signName: "阿里云短信测试专用"