spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ***************************************************************************************************************************************************
    username: dhr_ai_test
    password: dhr_ai_test@321
    driver-class-name: com.mysql.cj.jdbc.Driver

#分页
pagehelper:
  helper-dialect: mariadb
  reasonable: false
  support-methods-arguments: true
  params: 'count=countSql'


mybatis-plus:
  # 扫描 mapper.xml
  mapper-locations: classpath:mappers/**/*.xml  #也可以不配置，在代码中设置
  type-aliases-package: com.deloitte.dhr.ai.module.**.domain
  type-enums-package: com.deloitte.dhr.ai.enums
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


sap:
  #*************
  jcoAsHost: MTAxLjEzMi43MS4xMDU=
  #jcoAsHost: MTAuMS4xMjUuMjg=
  jcoSysnr: 00
  jcoClient: 110
  #************
  jcoClientRouter: ''
  jcoUser: SFJfRUND
  #HR_ECC
  jcoPasswd: MTIzNDU2Nzg=
  #12345678
  jcoLang: ZH
  jcoPoolCapacity: 30
  jcoPeakLimit: 10
  jcoMsHost: ''
  jcoMsServ: ''
  jcoGroup: ''
  jcoR3name: ''


ai:
  model:
    base-url: http://***********/v1
    agents:
      ssc_intent:
        suffix-url: /workflows/run
        api-key: app-Q9VrlNq3fouGqLFYlsZLmpnd
      ssc_rag:
        suffix-url: /workflows/run
        api-key: app-u99d2LeLKA8saORoWvpRyOQu
      data_analysis:
        suffix-url: /chat-messages
        api-key: app-y4X41f54vpOxvCoqucE9VdQd
      data_prediction:
        suffix-url: /chat-messages
        api-key: app-k6pntVU0dVsX15nnOZO9qrNv
      train_framework_generate:
        suffix-url: /workflows/run
        api-key: app-1s1i090Xs9Lt1KmqgWnwDGrO
      train_question_generate:
        suffix-url: /workflows/run
        api-key: app-w4C5vtVaDTWU7dDxcFVF6wgQ
      train_chat_analyze:
        suffix-url: /workflows/run
        api-key: app-LYTDrAaZkH9Zvn5tHpf6hD1B
      train_text_to_speech:
        suffix-url: /workflows/run
        api-key: app-7tl05aHln5SA2xZUkSB7d1Eh
      train_tag_extractor:
        suffix-url: /workflows/run
        api-key: app-VNmtRbyO4sc3MZ0S0opGZOeO
      teach_question_generate:
        suffix-url: /workflows/run
        api-key: app-zhCiPoSM6IOBSg8kR8bIQ9FX
      teach_question_evaluate:
        suffix-url: /workflows/run
        api-key: app-PAf3bTPEVV01bJ60kMr0rb9K
      teach_question_evaluate_batch:
        suffix-url: /workflows/run
        api-key: app-ggwaIy727dR2lntNuVJoNk8H
      duo:
        suffix-url: /chat-messages
        api-key: app-y2le2nnOIQuswPa723XobdrP
      interview_framework_generate:
        suffix-url: /workflows/run
        api-key: app-Zhp3mqkkVfG8KOz91ERaO8S8
      interview_answer_analyze:
        suffix-url: /workflows/run
        api-key: app-59UUmD7H7IwhsyI2KVcrHx62
      interview_report_analyze:
        suffix-url: /workflows/run
        api-key: app-K3kOWsRgVlYTTJFl7BRTpNt3
      digital_human_lining_intent:
        suffix-url: /workflows/run
        api-key: app-D8lHU7XXw30wdjF9ZQs1peiv
      digital_human_lining_normal:
        suffix-url: /workflows/run
        api-key: app-onXk4tjoRb7Q0IMbIDCdxIhc
      digital_human_lining_rag:
        suffix-url: /workflows/run
        api-key: app-bvEUxc0ujeEmSzeAWGJ6uK1z
    datasets:
      api-key: dataset-brpBY8Qgi8U56mi1MUQxMGpd
      suffix-url:
        create_by_file: /datasets/{dataset_id}/document/create-by-file
      datasetIds:
        coach: 498c416b-19d9-49c6-9461-f70da0780ce4
  tts:
    url: https://openspeech.bytedance.com/api/v1/tts
    token: dsjFBAgPGZk1TlQJRH6teWpuycF-hLt5
    paramGroup:
      default:
        appId: 5755125126
        cluster: volcano_tts
        voiceType: BV008_streaming
        encoding: mp3
        enable: true
      custom:
        appId: 5755125126
        cluster: volcano_icl
        voiceType: S_VeGmhzz91
        encoding: mp3
        enable: false

aliyun:
  sr:
    appKey: 4QoNr6s3mYOp7gU1
    access_key_id: LTAI5t5zxt353NWP65WtpVGP
    access_key_secret: ******************************
    url:
      asrUrl: https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/asr
      flashRecognizerUrl: https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/FlashRecognizer