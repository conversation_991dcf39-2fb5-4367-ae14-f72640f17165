spring:
  config:
    activate:
      on-profile: test
  #邮件
  mail:
    host: mx1.deloittecn.iphmx.com
    username: <EMAIL>
    default-encoding: UTF-8
    port: 25
    properties:
      mail:
        smtp:
          auth: false
          ssl:
            enable: false
    attachment-path: /home/<USER>/master/DHR_SLIMMING/utility_1.1/temp

  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB
      location: /data/upload_tmp

  redis:
    host: ***********
    port: 6379
    password: dhr123

  datasource:
    url: jdbc:mysql://************:3306/dhr_utility_test?characterEncoding=utf8&useUnicode=true&useSSL=false&serverTimezone=GMT%2B8
    username: dhr_utility_test
    password: dhr_utility_test321
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: DatebookHikariCP
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 300000
      connection-test-query: SELECT 1
  #flyway数据库版本管理工具
  flyway:
    # 启用或禁用 flyway
    enabled: false
    # 禁止删除指定schema下的所有table
    clean-disabled: true
    # SQL 脚本的目录,多个路径使用逗号分隔
    locations: classpath:db/migration
    #  metadata 版本控制信息表 默认 flyway_schema_history
    table: flyway_schema_history
    # 如果没有 flyway_schema_history 这个 metadata 表， 在执行 flyway migrate 命令之前, 必须先执行 flyway baseline 命令
    # 设置为 true 后 ,当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # 指定 baseline 的版本号,默认值为 1.0, 低于该版本号的 SQL 文件, migrate 时会被忽略
    baseline-version: 1.1
    # 字符编码 默认 UTF-8
    encoding: UTF-8
    # 是否允许不按顺序迁移 开发建议 true  生产建议 false
    out-of-order: false
    # 执行迁移时是否自动调用验证
    validate-on-migrate: true
#通用mapper
mapper:
  not-empty: false
  identity: mariadb
  mappers:
    - tk.mybatis.mapper.common.Mapper

#mybatis
mybatis:
  type-aliases-package: com.deloitte.dhr.utility.entity
  mapper-locations:
    - classpath*:mapper/*.xml

#分页
pagehelper:
  helper-dialect: mariadb
  reasonable: false
  support-methods-arguments: true
  params: 'count=countSql'


sap:
  #*************
  jcoAsHost: MTkyLjE2OC43MC42NQ==
  #jcoAsHost: MTAuMS4xMjUuMjg=
  jcoSysnr: 03
  jcoClient: 110
  #************
  jcoClientRouter: L0gvNDcuOTcuMTAzLjE0
  jcoUser: SFJfRUND
  #HR_ECC
  jcoPasswd: MTIzNDU2Nzg=
  #12345678
  jcoLang: ZH
  jcoPoolCapacity: 30
  jcoPeakLimit: 10
  jcoMsHost: ''
  jcoMsServ: ''
  jcoGroup: ''
  jcoR3name: ''

#文件
file:
  charset: UTF-8
  access:
    server: http://************/
    port: 80
  tracker:
    server: ************:22122
  pool:
    size: 100
  connect:
    timeout: 5000
  network:
    timeout: 30000
  anti:
    steal:
      token: no

hrService:
  servicePath: https://xxx.xxx.com/
  servicePathMobile: https://xxx.xxx.com/

aliyunoss:
  endpoint: https://oss-cn-shenzhen.aliyuncs.com
  accessKeyId: LTAI4GC2ouE7dkDe8a5NmZrv
  accessKeySecret: ******************************
  bucket: saphrtest
  saveHtPath: htyl
  readHtPath: ht

WcyunOss:
  endpoint: jtrs-oss-dev.oss-cn-hz-wcy-d01-a.ops.cloud.wzgroup.cn
  accessKeyId: LCvvjYZ4IkpotOIE
  accessKeySecret: ******************************
  bucketName: jtrs-oss-dev
  urlPrefix: jtrs-oss-dev.oss-cn-hz-wcy-d01-a.ops.cloud.wzgroup.cn

#dhr文件服务下载接口地址
dhr:
  env:
    downloadUrl: https://pscdev.deloitte.com.cn/dhr/test/dhrDownloadService/utility/file/dfs/download
    previewUrl: https://pscdev.deloitte.com.cn/dhr/test/dhrDownloadService/utility/file/convert/preview/

deloitte:
  sms:
    aliyun:
      access-key-id: LTAI5t8Y94K6hBLm6KEfem7H
      access-secret: ******************************
      sign-name: 德勤产品与解决方案中心

feign:
  hystrix:
    enabled: true

minio:
  endpoint: 172.16.4.93
  port: 9000
  accessKey: dsads213!dsa12gg
  secretKey: dhr123456
  secure: false
  bucketName: "dhr-test"

# 短链接相关
basepath: https://pscdev.deloitte.com.cn/survey/uat/ruoyi-api/dwz

file-view-url: https://pscdev.deloitte.com.cn/dhr/dev/view