spring:
  config:
    activate:
      on-profile: test

  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms
    lettuce:
      pool:
        min-idle: 10
        max-idle: 80
        max-wait: -1
        max-active: 80
  datasource:
    url: ***************************************************************************************************************************************
    username: dhr_longi_poc_peformance_test
    password: dhr_longi_poc_peformance_test321
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: DatebookHikariCP
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 300000
      connection-test-query: SELECT 1



  jpa:
    # database-platform: org.hibernate.dialect.Oracle10gDialect
    database-platform: org.hibernate.dialect.MariaDB103Dialect
    hibernate:
      ddl-auto: update #validate
    show-sql: true

elk:
  logstash:
    dest: ***********:5000

sap:
  #*************
  jcoAsHost: MTAxLjEzMi43MS4xMDU=
  #jcoAsHost: MTAuMS4xMjUuMjg=
  jcoSysnr: 00
  jcoClient: 110
  #************
  jcoClientRouter: ''
  jcoUser: SFJfRUND
  #HR_ECC
  jcoPasswd: MTIzNDU2Nzg=
  #12345678
  jcoLang: ZH
  jcoPoolCapacity: 30
  jcoPeakLimit: 10
  jcoMsHost: ''
  jcoMsServ: ''
  jcoGroup: ''
  jcoR3name: ''
# dhr服务前缀
dhrCoreService:
  rootPath: https://pscdev.deloitte.com.cn/dhr/test/web/#/

logging:
  level:
    root: info