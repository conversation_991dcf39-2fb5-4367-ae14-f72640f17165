spring:
  config:
    activate:
      on-profile: test
  boot:
    admin:
      client:
        url: lb://dhr-admin-service

  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ****************************************************************************************************************************************************
    username: dhr_ssc_test
    password: dhr_ssc_test321
    # url: ***************************************************************************************************************************************************
    # username: dhr_ssc_dev
    # password: dhr_ssc_dev321
    #    username: dhr_yx_basic_dev
    #    password: yx_basic_dev321
    driver-class-name: com.mysql.cj.jdbc.Driver
  freemarker:
    template-loader-path: classpath:/template
    suffix: .html
    prefer-file-system-access: false

  flyway:
    # 启用或禁用 flyway
    enabled: true
    # 禁止删除指定schema下的所有table
    clean-disabled: true
    # SQL 脚本的目录,多个路径使用逗号分隔
    locations: classpath:db/migration
    #  metadata 版本控制信息表 默认 flyway_schema_history
    table: flyway_schema_history
    # 如果没有 flyway_schema_history 这个 metadata 表， 在执行 flyway migrate 命令之前, 必须先执行 flyway baseline 命令
    # 设置为 true 后 ,当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # 指定 baseline 的版本号,默认值为 1.0, 低于该版本号的 SQL 文件, migrate 时会被忽略
    baseline-version: 1.1
    # 字符编码 默认 UTF-8
    encoding: UTF-8
    # 是否允许不按顺序迁移 开发建议 true  生产建议 false
    out-of-order: false
    # 执行迁移时是否自动调用验证
    validate-on-migrate: true

#分页
pagehelper:
  helper-dialect: mariadb
  reasonable: false
  support-methods-arguments: true
  params: 'count=countSql'


elk:
  logstash:
    dest: ***********:5000

sap:
  #*************
  jcoAsHost: MTAxLjEzMi43MS4xMDU=
  #jcoAsHost: MTAuMS4xMjUuMjg=
  jcoSysnr: 00
  jcoClient: 110
  #************
  jcoClientRouter: ''
  jcoUser: SFJfRUND
  #HR_ECC
  jcoPasswd: MTIzNDU2Nzg=
  #12345678
  jcoLang: ZH
  jcoPoolCapacity: 30
  jcoPeakLimit: 10
  jcoMsHost: ''
  jcoMsServ: ''
  jcoGroup: ''
  jcoR3name: ''


ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000


#不需要验证码效验
verifyCode:
  enable: false


feign:
  hystrix:
    enabled: true  #开启Feign的熔断功能
  client:
    config:
      default:
        connectTimeout: 10000  #连接超时时间10秒
        readTimeout: 20000     #读超时时间10秒
hystrix:
  command:
    default:
      execution:
        # 执行时候超时的最大上限，单位是毫秒，如果命令执行耗时超过此时间值那么会进入降级逻辑
        timeout:
          enabled: false
        isolation:
          # 信号量隔离，默认线程池隔离会造成request对象失效
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 100000
management:
  endpoints:
    web:
      exposure:
        include: '*'

mybatis-plus:
  # 扫描 mapper.xml
  mapper-locations: classpath:mappers/**/*.xml  #也可以不配置，在代码中设置
  type-aliases-package: com.deloitte.dhr.ssc.module.**.domain
  #  configuration:
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-enums-package: com.deloitte.dhr.ssc.enums
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    variables:
    #不同环境库名
      mdaDbName: "`dhr_mda_test`"


dhr:
  empInfoTable: dhr_mda_test.dhr_dt_employee_info
  mda:
    schema: dhr_mda_test
  permission:
    enable: true

login:
  type: mda


file-view-url: https://pscdev.deloitte.com.cn/dhr/dev/view/

minio:
  endpoint: 172.16.4.93
  port: 9000