spring:
  config:
    activate:
      on-profile: test
  boot:
    admin:
      client:
        url: lb://dhr-admin-service

  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *******************************************************************************************************************************************************
    username: dhr_talent_test
    password: mkKebQ#uaetR
    driver-class-name: com.mysql.cj.jdbc.Driver


  freemarker:
    template-loader-path: classpath:/template
    suffix: .ftl
    prefer-file-system-access: false

#分页
pagehelper:
  helper-dialect: mariadb
  reasonable: false
  support-methods-arguments: true
  params: 'count=countSql'


elk:
  logstash:
    dest: ***********:5000

sap:
  #*************
  jcoAsHost: MTAxLjEzMi43MS4xMDU=
  #jcoAsHost: MTAuMS4xMjUuMjg=
  jcoSysnr: 00
  jcoClient: 110
  #************
  jcoClientRouter: ''
  jcoUser: SFJfRUND
  #HR_ECC
  jcoPasswd: MTIzNDU2Nzg=
  #12345678
  jcoLang: ZH
  jcoPoolCapacity: 30
  jcoPeakLimit: 10
  jcoMsHost: ''
  jcoMsServ: ''
  jcoGroup: ''
  jcoR3name: ''


ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000


#不需要验证码效验
verifyCode:
  enable: false


feign:
  hystrix:
    enabled: true  #开启Feign的熔断功能
  client:
    config:
      default:
        connectTimeout: 30000  #连接超时时间10秒
        readTimeout: 30000     #读超时时间10秒
hystrix:
  command:
    default:
      execution:
        # 执行时候超时的最大上限，单位是毫秒，如果命令执行耗时超过此时间值那么会进入降级逻辑
        timeout:
          enabled: false
        isolation:
          # 信号量隔离，默认线程池隔离会造成request对象失效
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 100000
management:
  endpoints:
    web:
      exposure:
        include: '*'

mybatis-plus:
  # 扫描 mapper.xml
  mapper-locations: classpath:mappers/**/*.xml  #也可以不配置，在代码中设置
  type-aliases-package: com.deloitte.dhr.talent.module.**.domain
  #  configuration:
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-enums-package: com.deloitte.dhr.talent.enums
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

360Evaluation:
  questionnaire-url: https://pscdev.deloitte.com.cn/dhr/test/web/#/dhr2/360Evaluation/questionnaireAnswer
  mobile-questionnaire-url: https://pscdev.deloitte.com.cn/dhr/test/rc-app/#/360-evaluation/welcome
  selfSelection-url: https://pscdev.deloitte.com.cn/dhr/test/web/#/ess/backlog
  mobile-selfSelection-url: https://pscdev.deloitte.com.cn/dhr/test/app/#/notice/todo
  download-url: https://pscdev.deloitte.com.cn/dhr/test/dhrDownloadService

dhr:
  mda:
    schema: dhr_mda_test
  report:
    selenium-server: http://172.16.5.30:4445
  reportUrl: https://pscdev.deloitte.com.cn/dhr/test/gateway/dhr-utility-service/utility/file/dfs/preview?fileId=