spring:
  config:
    activate:
      on-profile: test
  boot:
    admin:
      client:
        url:  lb://dhr-admin-service

  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

  datasource:
    url: **************************************************************************************************************************
    username: dhr_yx_basic_dev
    password: yx_basic_dev321
#    username: dhr_yx_basic_dev
#    password: yx_basic_dev321
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    hikari:
      pool-name: DatebookHikariCP
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 300000
      connection-test-query: SELECT 1


#分页
pagehelper:
  helper-dialect: mariadb
  reasonable: false
  support-methods-arguments: true
  params: 'count=countSql'


elk:
  logstash:
    dest: ***********:5000

sap:
  #*************
  jcoAsHost: MTAxLjEzMi43MS4xMDU=
  #jcoAsHost: MTAuMS4xMjUuMjg=
  jcoSysnr: 00
  jcoClient: 110
  #************
  jcoClientRouter: ''
  jcoUser: SFJfRUND
  #HR_ECC
  jcoPasswd: MTIzNDU2Nzg=
  #12345678
  jcoLang: ZH
  jcoPoolCapacity: 30
  jcoPeakLimit: 10
  jcoMsHost: ''
  jcoMsServ: ''
  jcoGroup: ''
  jcoR3name: ''

#邮件配置
mail:
  dhr:
    smtp:
      host: mx1.deloittecn.iphmx.com
      port: 25
      auth: true
    userName: <EMAIL>
    password:

#短信配置
sms:
  url: http://***********:8012/sendSms.cgi

hrService:
  servicePath: https://pscdev.deloitte.com.cn/dhr/dev/web/
  servicePathMobile: https://pscdev.deloitte.com.cn/dhr/dev/app/

tableAu:
  wgserver: http://************:8000

#phantomjs 请求地址
phantomjs:
  url: http://***********:4444/wd/hub


ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000

#OCR 身份证识别服务端地址
ocr:
  #host地址
  host: https://cardpack.market.alicloudapi.com
  #识别身份证地址
  idCardPath: /rest/160601/ocr/ocr_idcard.json
  #识别银行卡地址
  bankCardPath: /rest/160601/ocr/ocr_bank_card.json
  #appCode
  appCode: 951c1401cd0742e7ba3fcea83e69b262

com:
  deloitte:
    # 阿里人脸识别
    compareface:
      regionId: cn-shanghai
      accessKeyId: LTAI5tA4PQia9WbpQEFHRkRq
      accessKeySecret: ******************************
      # 匹配度值大于等于该值才能判定为同一个人
      confidence: 80

#需要验证码效验
verifyCode:
  enable: false


# Es 配置
elasticsearch:
  # 请求协议
  schema: http
  # 集群名称
  clusterName: my-application
  # 集群节点
  clusterNodes:
    - ***********:9200
  # 连接超时时间(毫秒)
  connectTimeout: 1000
  # socket 超时时间
  socketTimeout: 30000
  # 连接请求超时时间
  connectionRequestTimeout: 500
  # 每个路由的最大连接数量
  maxConnectPerRoute: 10
  # 最大连接总数量
  maxConnectTotal: 30
  index:
    numberOfShards: 3
    numberOfReplicas: 0

feign:
  hystrix:
    enabled: true  #开启Feign的熔断功能
  client:
    config:
      default:
        connectTimeout: 10000  #连接超时时间10秒
        readTimeout: 20000     #读超时时间10秒
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 50000 #设置熔断时间50秒
management:
  endpoints:
    web:
      exposure:
        include: '*'

mybatis-plus:
  # 扫描 mapper.xml
  mapper-locations: classpath:mappers/*.xml #也可以不配置，在代码中设置
  type-aliases-package: com.deloitte.dhr.task.domain.**
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
reTry:
  error-email: <EMAIL>

# 泛微OA地址
oa:
  server: https://oatest.eswincomputing.com/
  app-id: E5750A97-66E3-4A94-95E7-B44838332BD1
  template:
    #010 模板用的categoryId
    template010: 3208270486306435150
    #除开010 的categoryId
    templateOther: 3152595521601651462