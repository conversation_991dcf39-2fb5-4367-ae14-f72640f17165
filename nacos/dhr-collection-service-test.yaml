spring:
  config:
    activate:
      on-profile: test
  boot:
    admin:
      client:
        url:  lb://dhr-admin-service

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: dhr_yx_basic_dev
    password: yx_basic_dev321
#    username: root
#    password: Init@1234

# Es 配置
elasticsearch:
  # 请求协议
  schema: http
  # 集群名称
  clusterName: my-application
  # 集群节点
  clusterNodes:
    - ***********:9200
  # 连接超时时间(毫秒)
  connectTimeout: 1000
  # socket 超时时间
  socketTimeout: 30000
  # 连接请求超时时间
  connectionRequestTimeout: 500
  # 每个路由的最大连接数量
  maxConnectPerRoute: 10
  # 最大连接总数量
  maxConnectTotal: 30
  index:
    numberOfShards: 3
    numberOfReplicas: 0


mybatis-plus:
  # 扫描 mapper.xml
  mapper-locations: classpath:mappers/*.xml #也可以不配置，在代码中设置
  type-aliases-package: com.deloitte.dhr.collection.model.**
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


feign:
  hystrix:
    enabled: true  #开启Feign的熔断功能
  client:
    config:
      default:
        connectTimeout: 10000  #连接超时时间10秒
        readTimeout: 20000     #读超时时间10秒
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 50000 #设置熔断时间50秒
ribbon:
  eager-load:
    enabled: true

dhr:
  userList: 00000001,00000002,00000042