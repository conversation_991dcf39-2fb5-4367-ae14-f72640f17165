spring:
  boot:
    admin:
      client:
        url:  http://***********:8037
        instance:
          prefer-ip: true
        username: dhr
        password: dhr666
  datasource:
    url: **********************************************************************************************************************
    username: dhr_mda_test
    password: LI@J!lZsgJ0u
    # url: **************************************************************************************************************************
    # username: dhr_yx_basic_dev
    # password: yx_basic_dev321
    driver-class-name: com.mysql.cj.jdbc.Driver

  redis:
    host: ***********
    port: 6379
    password: dhr123
    timeout: 5000ms

feign:
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types[0]: text/xml
      mime-types[1]: application/xml
      mime-types[2]: application/json
    response:
      enabled: true
  client:
    config:
      default:
        connectTimeout: 360000
        readTimeout: 360000
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 50000

mybatis-plus:
  mapper-locations: classpath:mappers/*.xml
  type-aliases-package: com.deloitte.dhr.security.uaa.model.**
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
logout:
  url: https://pscdev.deloitte.com.cn/dhr/test/web/#/login
  app-url: https://pscdev.deloitte.com.cn/dhr/test/app/#/login

login:
  type: mda

management:
  endpoints:
    web:
      exposure:
        include: '*'         
  endpoint:
    health:
      show-details: ALWAYS

oauth: 
  defaultPassword: Test&18@29