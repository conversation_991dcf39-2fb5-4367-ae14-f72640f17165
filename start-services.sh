#!/bin/bash

# DHR 微服务启动脚本
# 作者：AI Assistant
# 日期：2025-08-25

echo "=== DHR 微服务启动脚本 ==="
echo "开始启动DHR微服务..."

# 设置环境变量
export config_nacos_serveraddr=localhost
export config_nacos_port=8848
export config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
export config_profile=test

# JVM参数
JVM_ARGS="-Dotel.javaagent.enabled=false -Dotel.sdk.disabled=true"

# 检查Nacos是否运行
echo "检查Nacos服务状态..."
if ! lsof -i :8848 > /dev/null 2>&1; then
    echo "❌ Nacos服务未运行，请先启动Nacos服务"
    exit 1
fi
echo "✅ Nacos服务正在运行"

# 服务列表和端口配置
declare -A SERVICES=(
    ["dhr-oauth-service"]="9021"
    ["dhr-gateway-service"]="9020"
)

# 启动服务的函数
start_service() {
    local service_name=$1
    local port=$2
    
    echo "启动服务: $service_name (端口: $port)"
    
    # 检查端口是否被占用
    if lsof -i :$port > /dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用，跳过 $service_name"
        return 1
    fi
    
    # 设置服务端口
    export config_server_port=$port
    
    # 启动服务
    nohup mvn spring-boot:run \
        -pl "dhr-service/$service_name" \
        -Dspring-boot.run.jvmArguments="$JVM_ARGS" \
        -Dspring-boot.run.profiles=test \
        > "logs/${service_name}.log" 2>&1 &
    
    local pid=$!
    echo $pid > "logs/${service_name}.pid"
    
    echo "✅ $service_name 启动中... PID: $pid"
    echo "   日志文件: logs/${service_name}.log"
    echo "   PID文件: logs/${service_name}.pid"
    
    return 0
}

# 创建日志目录
mkdir -p logs

echo ""
echo "开始启动服务..."
echo ""

# 按顺序启动服务
for service in "${!SERVICES[@]}"; do
    port=${SERVICES[$service]}
    start_service "$service" "$port"
    echo ""
    sleep 2
done

echo "=== 启动完成 ==="
echo ""
echo "服务状态检查："
echo "请等待约30秒后检查服务状态"
echo ""
echo "检查命令："
echo "  tail -f logs/dhr-oauth-service.log     # 查看OAuth服务日志"
echo "  tail -f logs/dhr-gateway-service.log   # 查看网关服务日志"
echo ""
echo "停止服务："
echo "  ./stop-services.sh"
echo ""
