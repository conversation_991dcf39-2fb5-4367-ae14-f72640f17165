#!/bin/bash

# DHR 微服务停止脚本
# 作者：AI Assistant
# 日期：2025-08-25

echo "=== DHR 微服务停止脚本 ==="

# 服务列表
SERVICES=(
    "dhr-oauth-service"
    "dhr-gateway-service"
)

# 停止服务的函数
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo "停止服务: $service_name (PID: $pid)"
            kill $pid
            sleep 2
            
            # 如果进程还在运行，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                echo "强制停止: $service_name"
                kill -9 $pid
            fi
            
            echo "✅ $service_name 已停止"
        else
            echo "⚠️  $service_name 进程不存在 (PID: $pid)"
        fi
        rm -f "$pid_file"
    else
        echo "⚠️  未找到 $service_name 的PID文件"
    fi
}

echo "开始停止服务..."
echo ""

# 停止所有服务
for service in "${SERVICES[@]}"; do
    stop_service "$service"
done

echo ""
echo "=== 停止完成 ==="
