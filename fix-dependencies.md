# DHR 项目依赖修复指南

## 当前问题总结

### 1. 编译问题
- **FastDFS依赖不匹配**: `dhr-utility-provider` 使用老版本API
- **MyBatis Plus Model类**: `dhr-collection-api` 导入问题
- **PaginationInnerInterceptor**: MyBatis Plus版本兼容性
- **Aspose Words**: `dhr-basic-service` 缺少依赖

### 2. 启动问题
- **Nacos配置**: Spring Boot 3.x配置方式变化
- **加密配置**: OAuth服务的keystore配置
- **Bean工厂**: Spring Boot 3.x兼容性问题

## 修复步骤

### 步骤1: 修复FastDFS依赖
```bash
# 在 dhr-utility-provider/pom.xml 中添加正确的依赖
<dependency>
    <groupId>org.csource</groupId>
    <artifactId>fastdfs-client-java</artifactId>
    <version>1.29-SNAPSHOT</version>
</dependency>
```

### 步骤2: 修复MyBatis Plus配置
```bash
# 检查MyBatis Plus版本兼容性
# 可能需要降级到3.5.3版本或修改代码适配3.5.9
```

### 步骤3: 添加缺失的依赖
```bash
# 在相关pom.xml中添加Aspose Words依赖
# 注意：Aspose是商业软件，需要许可证
```

### 步骤4: 修复Spring Boot 3.x兼容性
```bash
# 检查所有@Configuration类
# 更新过时的配置方式
# 修复Bean定义问题
```

## 临时解决方案

### 方案1: 跳过有问题的模块
可以先启动以下相对简单的服务：
- dhr-xxl-job-admin-service (定时任务服务)
- 部分不依赖复杂组件的服务

### 方案2: 版本回退
考虑将Spring Boot版本从3.2.4回退到2.7.x版本，以提高兼容性。

### 方案3: 逐步修复
1. 先修复编译问题
2. 再解决启动配置问题
3. 最后处理运行时问题

## 建议的启动顺序

1. **基础服务**
   - Nacos (已运行)
   - dhr-xxl-job-admin-service

2. **核心服务**
   - dhr-oauth-service (认证)
   - dhr-gateway-service (网关)

3. **业务服务**
   - dhr-basic-service
   - dhr-mda-service
   - 其他业务服务

## 下一步行动

1. 选择修复策略（建议先尝试方案1）
2. 逐个修复编译问题
3. 测试服务启动
4. 解决运行时配置问题
