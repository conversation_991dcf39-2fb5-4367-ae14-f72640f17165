nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}

# Spring
spring:
  application:
    # 应用名称
    name: dhr-questionnaire-service
  config:
    activate:
      on-profile: ${config_profile:test}
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        # 配置中心地址
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        # 配置文件格式
        file-extension: yaml
        enabled: ${config.enable:true}
        group: ${spring.profiles.active}
        import-check:
          enabled: false

server:
  port: ${config_server_port:9045}

management:
  endpoints:
    web:
      exposure:
        include: '*'
