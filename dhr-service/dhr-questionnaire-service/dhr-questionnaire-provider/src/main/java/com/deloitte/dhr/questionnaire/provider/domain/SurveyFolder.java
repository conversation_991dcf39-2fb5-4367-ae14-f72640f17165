package com.deloitte.dhr.questionnaire.provider.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.Transient;

/**
 * <AUTHOR>
 * @date: 19/07/2021 14:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SurveyFolder extends BasicEntity {

    private String name;

    @Transient
    private Integer folderPaperCount = 0;

    /**
     * 国际化名称
     */
    @Transient
    private String localeName;
}
