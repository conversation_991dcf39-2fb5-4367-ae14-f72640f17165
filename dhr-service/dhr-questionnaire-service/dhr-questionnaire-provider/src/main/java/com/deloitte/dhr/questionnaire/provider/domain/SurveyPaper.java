package com.deloitte.dhr.questionnaire.provider.domain;

import com.deloitte.dhr.utility.api.dto.user.SapUser;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.Transient;
import java.time.LocalDateTime;

/**
 * @author: leon
 * @Date: 15/10/2019
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SurveyPaper extends BasicEntity {

    private String name;

    private String version;

    private String createSource;

    private String sourceId;

    private String status;

    private Integer questionCount;

    private String folderId;

    /**
     * 问卷预设二维码关联类型
     */
    private String linkType;

    /**
     * 问卷预设二维码关联的链接地址
     */
    private String paperLink;

    /**
     * 验证方式
     */
    private String validateWay;

    /**
     * 密码
     */
    private String validateValue;

    /**
     * 是否回显，1回显，0不回显
     */
    private Integer answerBack;

    private Integer repeatableFlag;

    /**
     * 问卷语言
     */
    private String locale;

    /**
     * 权限
     */
    @Transient
    private String authority;

    /**
     * 是否是创建者
     */
    @Transient
    private Boolean isOwner;

    @Transient
    private Long answerCount;

    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd HH-mm-ss")
    private LocalDateTime latestAnswerTime;

    /**
     * ip:下载ip
     * phone：下载手机号
     * email：下载邮箱
     */
    private String downloadOption;

    /**
     * 答题提醒
     * sms: 短信提醒
     * email: 邮件提醒
     */
    private String answerRemind;

    /**
     * 查看答题记录
     *  0: 不能查看
     *  1: 可以查看
     */
    private Integer viewAnswerHistory;

    /**
     * 是否开启logo
     */
    private Integer enableLogo;

    /**
     * logo地址
     */
    private String logoUrl;

    /**
     * 创建人信息
     */
    private SapUser createUser;

}
