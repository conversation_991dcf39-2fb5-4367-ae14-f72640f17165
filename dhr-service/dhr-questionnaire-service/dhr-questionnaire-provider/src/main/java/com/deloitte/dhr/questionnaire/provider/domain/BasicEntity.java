package com.deloitte.dhr.questionnaire.provider.domain;


import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import tk.mybatis.mapper.annotation.LogicDelete;

import jakarta.persistence.Id;

/**
 * <AUTHOR>
 */
@Data
public class BasicEntity implements Serializable {

	/**
	 * 主键
	 */
	@Id
	private String id;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 更新人
	 */
	private String updateBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * 是否禁用
	 */
	private Boolean disableFlag;

	/**
	 * 是否删除
	 */
	@LogicDelete
	private Boolean deleteFlag;

	/**
	 * 设置创建人信息
	 * @param creatorId
	 */
	public void setCreator(String creatorId) {
		this.createBy =  creatorId;
		this.createTime = new Date();
	}

	/**
	 * 设置跟新人信息
	 * @param updaterId
	 */
	public void setUpdater(String updaterId){
		this.updateBy = updaterId;
		this.updateTime = new Date();
	}

	/**
	 * 设置默认未删除和启用
	 */
	public void setDeleteAndDisableDefault(){
		this.deleteFlag = false;
		this.disableFlag = false;
	}

}
