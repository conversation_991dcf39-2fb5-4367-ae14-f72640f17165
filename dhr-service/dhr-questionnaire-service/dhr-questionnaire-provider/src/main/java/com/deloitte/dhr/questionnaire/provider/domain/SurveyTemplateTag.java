package com.deloitte.dhr.questionnaire.provider.domain;

/**
 * <AUTHOR>
 * @date: 21/07/2021 15:37
 */

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Transient;

/**
 * 问卷模板文件夹
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SurveyTemplateTag extends BasicEntity {

    private String name;

    @Transient
    private Integer templateCount;

    @Transient
    private String localeName;

}
