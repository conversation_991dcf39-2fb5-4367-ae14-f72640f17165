package com.deloitte.dhr.questionnaire.provider.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.Transient;

/**
 * <AUTHOR>
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class SurveyPaperTemplate extends BasicEntity{

    private static final long serialVersionUID = 1L;

    /** 名称 */
    private String name;

    /** 模板所属标签id */
    private String tagId;

    /** 问卷id */
    private String paperId;

    /** 是否公开,0没有公开,1表示公开 */
    private Boolean publicFlag;

    @Transient
    private Boolean editFlag ;

    /**
     * 题目数量
     */
    private Integer questionCount;
}

