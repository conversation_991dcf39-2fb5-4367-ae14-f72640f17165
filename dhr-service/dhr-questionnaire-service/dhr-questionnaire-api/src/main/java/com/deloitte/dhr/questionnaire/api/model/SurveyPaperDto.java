package com.deloitte.dhr.questionnaire.api.model;

import lombok.Data;
import lombok.ToString;

import jakarta.persistence.Transient;

/**
 * @author: leon
 * @Date: 15/10/2019
 * @Description:
 */
@Data
@ToString
public class SurveyPaperDto  {

    private String name;

    private String version;

    private String createSource;

    private String sourceId;

    private String status;

    private Integer questionCount;

    private String folderId;

    /**
     * 问卷预设二维码关联类型
     */
    private String linkType;

    /**
     * 问卷预设二维码关联的链接地址
     */
    private String paperLink;

    /**
     * 验证方式
     */
    private String validateWay;

    /**
     * 密码
     */
    private String validateValue;

    /**
     * 是否回显，1回显，0不回显
     */
    private Integer answerBack;

    private Boolean repeatableFlag;

    /**
     * 问卷语言
     */
    private String locale;

    /**
     * 权限
     */
    @Transient
    private String authority;

    /**
     * 是否是创建者
     */
    @Transient
    private Boolean isOwner;

    @Transient
    private Integer answerCount;



}
