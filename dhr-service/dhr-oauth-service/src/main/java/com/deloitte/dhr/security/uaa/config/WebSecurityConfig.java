package com.deloitte.dhr.security.uaa.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * 安全配置 - Spring Boot 3.x版本
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, prePostEnabled = true)
public class WebSecurityConfig {

    /**
     * 认证管理器 - Spring Boot 3.x版本
     *
     * @param authenticationConfiguration
     * @return
     * @throws Exception
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    /**
     * 密码编码器
     *
     * @return
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 安全过滤器链 - Spring Boot 3.x版本
     *
     * @param http
     * @return
     * @throws Exception
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(csrf -> csrf.disable())
                .authorizeHttpRequests(authz -> authz
                        // OAuth2相关端点
                        .requestMatchers("/auth/login", "/auth/authorize", "/oauth/**").permitAll()
                        // 静态资源
                        .requestMatchers("/img/**").permitAll()
                        // 登出和验证相关
                        .requestMatchers("/logout/**", "/validToken", "/user/**").permitAll()
                        // 其他请求需要认证
                        .anyRequest().authenticated()
                )
                .formLogin(form -> form
                        .loginPage("/auth/login") // 自定义登录页面
                        .loginProcessingUrl("/login") // 登录处理URL
                        .permitAll()
                )
                .httpBasic(httpBasic -> {}) // 启用HTTP Basic认证
                .headers(headers -> headers
                        .frameOptions(frameOptions -> frameOptions.disable()) // 禁用X-Frame-Options
                );

        return http.build();
    }

}
