package com.deloitte.dhr.security.uaa.config;

import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.client.JdbcRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.JdbcOAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import com.deloitte.dhr.security.uaa.ext.CustomTokenEnhancer;

import javax.sql.DataSource;
import java.time.Duration;

/**
 * 授权服务器配置 - Spring Boot 3.x版本
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Configuration
public class AuthorizationServer {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RSAKey rsaKey;

    /**
     * OAuth2授权服务器安全过滤器链
     */
    @Bean
    @Order(1)
    public SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http) throws Exception {
        OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);
        http.getConfigurer(OAuth2AuthorizationServerConfigurer.class)
                .oidc(Customizer.withDefaults()); // 启用OpenID Connect 1.0

        http.exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(new LoginUrlAuthenticationEntryPoint("/auth/login"))
        );

        return http.build();
    }

    /**
     * 注册客户端存储库 - 使用数据库存储
     */
    @Bean
    public RegisteredClientRepository registeredClientRepository() {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        JdbcRegisteredClientRepository repository = new JdbcRegisteredClientRepository(jdbcTemplate);

        // 初始化默认客户端（如果不存在）
        initializeDefaultClients(repository);

        return repository;
    }

    /**
     * OAuth2授权服务 - 使用数据库存储
     */
    @Bean
    public OAuth2AuthorizationService authorizationService() {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return new JdbcOAuth2AuthorizationService(jdbcTemplate, registeredClientRepository());
    }


    /**
     * JWK源配置
     */
    @Bean
    public JWKSource<SecurityContext> jwkSource() {
        JWKSet jwkSet = new JWKSet(rsaKey);
        return (jwkSelector, securityContext) -> jwkSelector.select(jwkSet);
    }

    /**
     * JWT解码器配置
     */
    @Bean
    public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
        return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
    }

    /**
     * 授权服务器设置
     */
    @Bean
    public AuthorizationServerSettings authorizationServerSettings() {
        return AuthorizationServerSettings.builder()
                .issuer("http://localhost:9006")
                .build();
    }

    /**
     * 令牌设置
     */
    @Bean
    public TokenSettings tokenSettings() {
        return TokenSettings.builder()
                .accessTokenTimeToLive(Duration.ofDays(30)) // 访问令牌有效期30天
                .refreshTokenTimeToLive(Duration.ofDays(30)) // 刷新令牌有效期30天
                .reuseRefreshTokens(true)
                .build();
    }

    /**
     * 客户端设置
     */
    @Bean
    public ClientSettings clientSettings() {
        return ClientSettings.builder()
                .requireAuthorizationConsent(false) // 不需要用户确认授权
                .build();
    }

    /**
     * JWT Token自定义器
     */
    @Bean
    public OAuth2TokenCustomizer<JwtEncodingContext> jwtTokenCustomizer() {
        return new CustomTokenEnhancer();
    }

    /**
     * 初始化默认客户端
     */
    private void initializeDefaultClients(JdbcRegisteredClientRepository repository) {
        // 这里可以添加默认客户端的初始化逻辑
        // 由于客户端信息已经在数据库中，这里暂时留空
        // 如果需要，可以从数据库的oauth_client_details表迁移数据到新的表结构
    }


}
