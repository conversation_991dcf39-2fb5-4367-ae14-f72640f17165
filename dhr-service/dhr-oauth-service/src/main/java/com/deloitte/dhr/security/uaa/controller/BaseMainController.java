package com.deloitte.dhr.security.uaa.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.base.constants.OauthConstant;
import com.deloitte.dhr.security.uaa.common.Constant;
import com.deloitte.dhr.security.uaa.service.UserService;
import com.deloitte.dhr.security.uaa.util.DriverUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 基础控制器
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
@Controller
@RefreshScope
@Slf4j
public class BaseMainController {

    /**授权头*/
    public static final String AUTHORIZATION = "authorization";
    /**授权标识*/
    public static final String BEARER = "Bearer ";
    /**登陆缓存标识*/
    public static final String LOGIN_CACHE = "login";
    /**手机标识*/
    public static final String MOBILE_TAG = "app";

    public static final String EN = "en";
    public static final String ZH = "zh";

    private static final String IV_MOBILE = "IV_MOBILE";
    private static final String IV_PC = "IV_PC";
    private static final String CONNECTOR_PREFIX = "_";



    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private OAuth2AuthorizationService authorizationService;


    /**登出地址*/
    @Value("${logout.url}")
    String logoutURL;
    /**APP登出地址*/
    @Value("${logout.app-url}")
    private String logoutAppURL;

    /**
     * 自定义登录界面
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param httpServletRequest
     * @return
     */
//    @GetMapping("/auth/login")
//    public ModelAndView listUser(HttpServletRequest httpServletRequest) {
//        Map<String, String> param = new HashMap<>();
//        param.put("contextPath", httpServletRequest.getContextPath());
//        return new ModelAndView("login", param);
//    }

    /**
     * 自定义登出
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param request
     * @param response
     */
    @RequestMapping("/logout/redirect")
    public void exit(HttpServletRequest request, HttpServletResponse response) throws NoSuchAlgorithmException {
        //pc端登出
        this.loginOut(request, response, null);
    }

    /**
     * 登出方法
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param request
     * @param response
     * @param app
     */
    public void loginOut(HttpServletRequest request, HttpServletResponse response, String app) throws NoSuchAlgorithmException {
        new SecurityContextLogoutHandler().logout(request, response, null);

        HttpSession session = request.getSession();
        if (request.getCookies() != null && request.getCookies().length > 0) {
            List<Cookie> collectCookie = Arrays.stream(request.getCookies()).filter(a -> "Authorization".equals(a.getName())).collect(Collectors.toList());
            String authorization = "";
            if (collectCookie.size() > 0) {
                authorization = collectCookie.get(0).getValue();
            }
            if (StrUtil.isEmpty(authorization)) {
                //从头header中获取令牌数据 Authorization Bearer token
                authorization = request.getHeader("Authorization");
                if (!StrUtil.isEmpty(authorization)) {
                    authorization = authorization.split(" ")[1];
                }
            }

            if (!StrUtil.isEmpty(authorization)) {
                String userName = UserService.getUserNameByAuthorization(authorization);
                String signature = UserService.getSignatureByAuthorization(authorization);

                //删除缓存
                if (!StrUtil.isEmpty(app) && MOBILE_TAG.equals(app)) {
                    Object obj = redisTemplate.opsForValue().get(MD5Utils.md5Hex(authorization.getBytes())+CONNECTOR_PREFIX+Constant.IV_MOBILE+CONNECTOR_PREFIX+EN);
                    redisTemplate.delete(DriverUtil.getLoginKey(Constant.IV_MOBILE+EN, userName, signature));
                    redisTemplate.delete(DriverUtil.getUserCacheKey(Constant.IV_MOBILE+EN, userName, signature));
                    redisTemplate.delete(DriverUtil.getLoginKey(Constant.IV_MOBILE+ZH, userName, signature));
                    redisTemplate.delete(DriverUtil.getUserCacheKey(Constant.IV_MOBILE+ZH, userName, signature));
                    redisTemplate.delete(MD5Utils.md5Hex(authorization.getBytes())+CONNECTOR_PREFIX+Constant.IV_MOBILE+CONNECTOR_PREFIX+EN);
                    redisTemplate.delete(MD5Utils.md5Hex(authorization.getBytes())+CONNECTOR_PREFIX+Constant.IV_MOBILE+CONNECTOR_PREFIX+ZH);
                } else {
                    redisTemplate.delete(DriverUtil.getLoginKey(Constant.IV_PC+EN, userName, signature));
                    redisTemplate.delete(DriverUtil.getUserCacheKey(Constant.IV_PC+EN, userName, signature));
                    redisTemplate.delete(DriverUtil.getLoginKey(Constant.IV_PC+ZH, userName, signature));
                    redisTemplate.delete(DriverUtil.getUserCacheKey(Constant.IV_PC+ZH, userName, signature));
                    redisTemplate.delete(MD5Utils.md5Hex(authorization.getBytes())+CONNECTOR_PREFIX+Constant.IV_PC+CONNECTOR_PREFIX+EN);
                    redisTemplate.delete(MD5Utils.md5Hex(authorization.getBytes())+CONNECTOR_PREFIX+Constant.IV_PC+CONNECTOR_PREFIX+ZH);
                }
            }

        }
        if (!Objects.isNull(session)) {
            log.info("session "+session);
            //删除cookie
            response.addCookie(createDelCookie("DHR_TOKEN"));
            response.addCookie(createDelCookie("Authorization"));
            //销毁session
            session.invalidate();
        }

        try {
            String auth = request.getHeader(AUTHORIZATION);
            if (!StrUtil.isEmpty(auth)) {
                String token = auth.replace(BEARER, "");
                // 在Spring Boot 3.x中，令牌撤销需要通过OAuth2AuthorizationService来实现
                // 这里暂时简化处理，直接返回成功状态
                // 实际实现需要根据具体的令牌存储策略来撤销令牌
                response.setStatus(HttpServletResponse.SC_OK);
                log.info("Token revocation requested for token: {}", token.substring(0, Math.min(10, token.length())) + "...");
            }
            if (!StrUtil.isEmpty(app) && MOBILE_TAG.equals(app)) {
                log.info("ApplogoutURL :: " + logoutAppURL);
                response.setHeader("Location", logoutAppURL);
                response.sendRedirect(logoutAppURL);
                return;
            }
            log.info("logoutURL :: " + logoutURL);
            String redirectUrl = request.getParameter("redirectUrl");
            if (StrUtil.isEmpty(redirectUrl)) {
                response.setHeader("Location", logoutURL);
                response.sendRedirect(logoutURL);
            } else {
                response.setHeader("Location", redirectUrl); //NOSONAR
                response.sendRedirect(redirectUrl);  //NOSONAR
            }
        } catch (IOException e) {
            log.error(e.getMessage());
        }

    }

    /**
     * APP端登出
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param request
     * @param response
     */
    @RequestMapping("/logout/app")
    public void loginOutApp(HttpServletRequest request, HttpServletResponse response) throws NoSuchAlgorithmException {
        //移动端登出
        this.loginOut(request, response, MOBILE_TAG);
    }

    /**
     * token效验
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param httpServletRequest
     * @return Boolean
     */
    @RequestMapping("/validToken")
    @ResponseBody
    public ResponseVO<Boolean> ValidToken(HttpServletRequest httpServletRequest) {
        String token = httpServletRequest.getParameter("token");
        Boolean flag = false;
        if (StrUtil.isEmpty(token)) {
            return ResponseVO.success(flag);
        }
        try {
            Algorithm algorithm = Algorithm.HMAC256(OauthConstant.OAUTH_SECRET);  //NOSONAR
            JWTVerifier verifier = JWT.require(algorithm).build();
            DecodedJWT jwt = verifier.verify(token);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVO.success(flag);
        }
        flag = true;
        return ResponseVO.success(flag);
    }

    /**
     * 创建删除的Cookie
     *
     * @author: LiYong
     * @version: 1.0
     * @date: 24/04/2022
     * @param cookieName
     * @return Cookie
     */
    private Cookie createDelCookie(String cookieName) {
        Cookie cookie = new Cookie(cookieName, null);
        cookie.setMaxAge(0);
        cookie.setPath("/");
        return cookie;
    }
}

