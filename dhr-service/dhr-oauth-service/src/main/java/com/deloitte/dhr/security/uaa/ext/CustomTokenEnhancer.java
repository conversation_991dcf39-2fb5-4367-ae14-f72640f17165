package com.deloitte.dhr.security.uaa.ext;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.deloitte.dhr.security.uaa.dto.UserDto;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.oidc.endpoint.OidcParameterNames;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;

/**
 * JWT Token自定义器 - Spring Boot 3.x版本
 * 用于在JWT中添加额外的用户信息
 *
 * @author: LiYong
 * @version: 1.0
 * @date: 24/04/2022
 */
public class CustomTokenEnhancer implements OAuth2TokenCustomizer<JwtEncodingContext> {

    @Override
    public void customize(JwtEncodingContext context) {
        if (OidcParameterNames.ID_TOKEN.equals(context.getTokenType().getValue()) ||
            "access_token".equals(context.getTokenType().getValue())) {

            Authentication authentication = context.getPrincipal();
            Object userObject = authentication.getPrincipal();

            UserDto userDto = null;

            if (userObject instanceof UserDetails) {
                UserDetails userDetail = (UserDetails) userObject;
                String userString = userDetail.getUsername();
                try {
                    userDto = JSON.parseObject(userString, UserDto.class);
                } catch (Exception e) {
                    // 如果解析失败，创建一个基本的UserDto
                    userDto = new UserDto();
                    userDto.setUsername(userDetail.getUsername());
                }
            } else {
                try {
                    userDto = JSONObject.parseObject(userObject.toString(), UserDto.class);
                } catch (Exception e) {
                    // 如果解析失败，创建一个基本的UserDto
                    userDto = new UserDto();
                    userDto.setUsername(userObject.toString());
                }
            }

            // 添加自定义声明到JWT
            JwtClaimsSet.Builder claims = context.getClaims();
            if (userDto != null) {
                claims.claim("staff_no", userDto.getEmployeeNumber());
                claims.claim("oaName", userDto.getOaName());
                claims.claim("user_name", userDto.getUsername());
                claims.claim("pageCode", userDto.getPageCode());
            }
        }
    }
}
