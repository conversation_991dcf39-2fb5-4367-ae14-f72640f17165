package com.deloitte.dhr.mda;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/** 开启定时任务 */
@EnableScheduling
@EnableFeignClients(basePackages = {"com.deloitte.dhr"})
@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = {"com.deloitte.dhr", "com.deloitte.dhr.excel.parser"})
public class DHRMdaApplication {
	public static void main(String[] args) {
		SpringApplication.run(DHRMdaApplication.class, args);
	}
}

