package com.deloitte.dhr.performance;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Performance
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@EnableScheduling
@EnableFeignClients(basePackages = {"com.deloitte.dhr.mda.module", "com.deloitte.bpm.provider", "com.deloitte.dhr.utility", "com.deloitte.dhr.ssc"})
@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = {"com.deloitte.dhr.common", "com.deloitte.dhr.performance", "com.deloitte.dhr.mda", "com.deloitte.bpm", "com.deloitte.dhr.utility", "com.deloitte.dhr.ssc", "com.deloitte.dhr.excel.parser"})
public class DHRPerformanceApplication {
    public static void main(String[] args) {
        SpringApplication.run(DHRPerformanceApplication.class, args);
    }
}
