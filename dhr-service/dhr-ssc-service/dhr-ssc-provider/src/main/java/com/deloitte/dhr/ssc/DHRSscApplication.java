package com.deloitte.dhr.ssc;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * SSC
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@EnableScheduling
@EnableFeignClients(basePackages = {"com.deloitte.dhr.mda.module", "com.deloitte", "com.deloitte.dhr.utility"})
@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = {"com.deloitte.dhr.common", "com.deloitte.dhr.ssc", "com.deloitte.dhr.mda", "com.deloitte.bpm", "com.deloitte.dhr.utility", "com.deloitte.dhr.excel.parser"})
public class DHRSscApplication {
    public static void main(String[] args) {
        SpringApplication.run(DHRSscApplication.class, args);
    }
}
