package com.deloitte.bpm.entity;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 流程适应范围
 * <AUTHOR>
 */
@Data
@Table(name = "BPM_SCOPE")
public class Scope {

    @Id
    @Column(name = "ID")
    private String id;

    /**
     * 编码
     */
    @Column(name = "CODE")
    private String code;
    /**
     * 名称
     */
    @Column(name = "NAME")
    private String name;
    /**
     * 类型 ：10:组织结构 20:人事范围 30:薪酬核算范围
     */
    @Column(name = "TYPE")
    private String type;

    /**
     * 来源系统编码
     */
    @Column(name = "SOURCE_SYS_CODE")
    private String sourceSysCode;

    /**
     * 来源系统名称
     */
    @Column(name = "SOURCE_SYS_NAME")
    private String sourceSysName;

    /**
     * 流程类型编码
     */
    @Column(name = "MENU_CODE")
    private String menuCode;

    /**
     * 流程类型名称
     */
    @Column(name = "MENU_NAME")
    private String menuName;

    /**
     * 流程子类型编码
     */
    @Column(name = "SUB_MENU_CODE")
    private String subMenuCode;

    /**
     * 流程子类型名称
     */
    @Column(name = "SUB_MENU_NAME")
    private String subMenuName;

}