package com.deloitte.bpm.entity;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *         流程url
 */
@Data
@Table(name = "BPM_PROCESS_URL")
public class ProcessUrl implements Serializable {

    private static final long serialVersionUID = 8772285852048293757L;

    @Id
    @Column(name = "ID")
    private String id;

    @Column(name = "GUID")
    private String guid;

    @Column(name = "COMPANYID")
    private String companyid;

    @Column(name = "URL")
    private String url;
    /**
     * 移动端url
     */
    @Column(name = "MURL")
    private String murl;


    @Column(name = "CREATE_USER")
    private String createUser;

    @Column(name = "UPDATE_USER")
    private String updateUser;

    @Column(name = "CREATE_TIME")
    private Date createTime;

    @Column(name = "UPDATE_TIME")
    private Date updateTime;

}