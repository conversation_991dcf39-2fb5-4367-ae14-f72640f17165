package com.deloitte.bpm.entity;

import com.deloitte.bpm.validator.AddGroup;
import com.deloitte.bpm.validator.EditGroup;
import com.deloitte.dhr.common.base.api.ApiModel;
import com.deloitte.dhr.common.base.api.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 流程基本信息
 *
 * <AUTHOR>
 */
@ApiModel(value = "processTenant", description = "流程基本信息")
@Data
@Table(name = "BPM_PROCESS")
public class ProcessTenant implements Serializable {

    private static final long serialVersionUID = -7348678010981091637L;

    @ApiModelProperty(name = "id", value = "记录编号(主键)")
    @Id
    @NotBlank(message = "记录编号不能为空", groups = {EditGroup.class})
    @Column(name = "ID")
    private String id;
    /**
     * 添加人
     */
    @ApiModelProperty(value = "添加人")
    @Column(name = "CREATE_USER")
    private String createUser;
    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间")
    @Column(name = "CREATE_TIME")
    private Date createTime;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 流程编号
     */
    @ApiModelProperty(name = "guid", value = "流程编码", required = true)
    @NotBlank(message = "流程编码不能为空", groups = {AddGroup.class})
    @Length(max = 40, message = "流程编码长度不能超过40个字符", groups = {AddGroup.class})
    @Column(name = "GUID")
    private String guid;

    /**
     * 流程说明/描述
     */
    @ApiModelProperty(name = "comments", value = "流程说明/描述")
    @Length(max = 400, message = "流程说明/描述长度不能超过400个字符", groups = {AddGroup.class, EditGroup.class})
    @Column(name = "COMMENTS")
    private String comments;

    /**
     * 流程名称
     */
    @ApiModelProperty(name = "name", value = "流程名称", required = true)
    @NotBlank(message = "流程名称不能为空", groups = {AddGroup.class})
    @Length(max = 100, message = "流程名称长度不能超过100个字符", groups = {AddGroup.class, EditGroup.class})
    @Column(name = "NAME")
    private String name;

    /**
     * 流程系统编码
     */
    @ApiModelProperty(name = "PROCESS_SYS_CODE", value = "流程系统编码")
    @NotBlank(message = "流程系统编码不能为空", groups = {AddGroup.class})
    @Column(name = "PROCESS_SYS_CODE")
    private String processSysCode;

    /**
     * 流程类型编码
     */
    @ApiModelProperty(name = "menuCode", value = "流程类型编码")
    @Column(name = "CATEGORY_CODE")
    private String menuCode;

    /**
     * 流程类型名称
     */
    @ApiModelProperty(name = "menuName", value = "流程类型名称")
    @Column(name = "CATEGORY_NAME")
    private String menuName;

    /**
     * 流程子类型编码
     */
    @ApiModelProperty(name = "subMenuCode", value = "流程子类型编码")
    @Column(name = "SUB_CATEGORY_CODE")
    private String subMenuCode;

    /**
     * 流程子类型名称
     */
    @ApiModelProperty(name = "subMenuName", value = "流程子类型名称")
    @Column(name = "SUB_CATEGORY_NAME")
    private String subMenuName;

    /**
     * 状态 0:停用 1:启用
     */
    @ApiModelProperty(name = "status", value = "状态 0:停用 1:启用")
    @Column(name = "STATUS")
    private Boolean status;
}