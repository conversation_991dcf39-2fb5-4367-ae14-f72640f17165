package com.deloitte.bpm.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Table(name = "BPM_SOURCE_SYSTEM")
public class SourceSystem {
    @Id
    @Column(name = "ID")
    private Object id;

    @Column(name = "SOURCE_SYS_CODE")
    private Object sourceSysCode;

    @Column(name = "SOURCE_SYS_NAME")
    private Object sourceSysName;

    @Column(name = "CREATE_TIME")
    private Date createTime;

    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * @return ID
     */
    public Object getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Object id) {
        this.id = id;
    }

    /**
     * @return SOURCE_SYS_CODE
     */
    public Object getSourceSysCode() {
        return sourceSysCode;
    }

    /**
     * @param sourceSysCode
     */
    public void setSourceSysCode(Object sourceSysCode) {
        this.sourceSysCode = sourceSysCode;
    }

    /**
     * @return SOURCE_SYS_NAME
     */
    public Object getSourceSysName() {
        return sourceSysName;
    }

    /**
     * @param sourceSysName
     */
    public void setSourceSysName(Object sourceSysName) {
        this.sourceSysName = sourceSysName;
    }

    /**
     * @return CREATE_TIME
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return UPDATE_TIME
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}