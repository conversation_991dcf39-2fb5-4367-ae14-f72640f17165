nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}

server:
  port: ${config_server_port:8099}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 2048
spring:
  application:
    name: dhr-bpm-service
  config:
    activate:
      on-profile: ${config_profile:test}
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        import-check:
          enabled: false

  activiti:
    database-schema-update: none  # 更新或创建表
    check-process-definitions: false #流程自动部署
    jpa-enabled: false
    async-executor-activate: false
    update-deploy: false  # 更新流程部署
    db-history-used: false
    db-identity-used: false
  mvc:
    date-format: yyyy-MM-dd
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
#mybatis
mybatis:
  type-aliases-package: com.deloitte.bpm.entity
  configuration:
    jdbc-type-for-null: 'NULL'
  mapper-locations:
    - classpath*:mapper/*.xml
#mapper
mapper:
  not-empty: false
  identity: mysql
  mappers:
    - tk.mybatis.mapper.common.Mapper
#pagehelper
pagehelper:
  helper-dialect: mysql
  reasonable: false
  support-methods-arguments: true
  params: 'count=countSql'
# logger
feign:
  httpclient:
    enabled: false
    max-connections: 100
    max-connections-per-route: 20
  okhttp:
    enabled: true
  compression:
    request:
      enabled: true
    response:
      enabled: true
  client:
    config:
      default:
        connect_timeout: 300000
        read_timeout: 300000
ribbon:
  eureka:
    enabled: true
  okhttp:
    enabled: true


# logger
logging:
  config: 'classpath:logback-spring.xml'
  level:
    root: info  #输出日志级别
# 密钥
aes:
  privateKey: ebhSd00L

