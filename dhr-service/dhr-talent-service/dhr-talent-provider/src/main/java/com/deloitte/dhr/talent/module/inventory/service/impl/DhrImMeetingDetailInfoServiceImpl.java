package com.deloitte.dhr.talent.module.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Lists;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.Request;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.MessageUtils;
import com.deloitte.dhr.excel.parser.handler.I18nCellWriteHandler;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheet;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheetData;
import com.deloitte.dhr.excel.parser.template.ExcelParserTemplate;
import com.deloitte.dhr.mda.module.base.EmployeeFromPersonnelWarehouseFeignInterface;
import com.deloitte.dhr.mda.module.base.pojo.EmployeeFromPersonnelWarehouseRequest;
import com.deloitte.dhr.mda.module.base.pojo.EmployeeFromPersonnelWarehouseResponse;
import com.deloitte.dhr.talent.module.inventory.constant.DhrMaConstant;
import com.deloitte.dhr.talent.module.inventory.domain.DhrImChangeHistory;
import com.deloitte.dhr.talent.module.inventory.domain.DhrImMeetingDetailInfo;
import com.deloitte.dhr.talent.module.inventory.domain.DhrMaActivityInfo;
import com.deloitte.dhr.talent.module.inventory.domain.DhrMaInventoryObject;
import com.deloitte.dhr.talent.module.inventory.excel.dto.DhrImMeetingDetailInfoDTO;
import com.deloitte.dhr.talent.module.inventory.mapper.DhrImMeetingDetailInfoMapper;
import com.deloitte.dhr.talent.module.inventory.mapper.DhrMaInventoryObjectMapper;
import com.deloitte.dhr.talent.module.inventory.pojo.*;
import com.deloitte.dhr.talent.module.inventory.service.*;
import com.deloitte.dhr.talent.util.CheckModelUtil;
import com.deloitte.dhr.talent.util.TalentFeignMethodUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * dhr_im_meeting_detail_info人才盘点 - 盘点会议 - 盘点会议详情主表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Service
public class DhrImMeetingDetailInfoServiceImpl extends SuperServiceImpl<DhrImMeetingDetailInfoMapper, DhrImMeetingDetailInfo> implements DhrImMeetingDetailInfoService {

    @Autowired
    private DhrImMeetingDetailInfoMapper dhrImMeetingDetailInfoMapper;

    @Autowired
    private DhrMaInventoryObjectService dhrMaInventoryObjectService;

    @Autowired
    private DhrMaInventoryObjectMapper dhrMaInventoryObjectMapper;

    @Autowired
    private DhrMaActivityInfoService dhrMaActivityInfoService;

    @Autowired
    private DhrImChangeHistoryService dhrImChangeHistoryService;

    @Autowired
    private DhrMaConvertRuleService dhrMaConvertRuleService;

    @Autowired
    private ExcelParserTemplate excelParserTemplate;

    @Autowired
    private TalentFeignMethodUtil talentFeignMethodUtil;

    /**
     * 最近次数
     */
    private static final Integer RECENT_TIMES = 3;

    @Autowired
    private EmployeeFromPersonnelWarehouseFeignInterface employeeFromPersonnelWarehouseFeignInterface;

    @Override
    public DhrImMeetingDetailInfoResponse getById(Long id) {
        DhrImMeetingDetailInfo po = this.get(id);
        DhrImMeetingDetailInfoResponse response = new DhrImMeetingDetailInfoResponse();
        BeanUtils.copyProperties(po, response);
        return response;
    }

    @Override
    public DhrImMeetingDetailRuleResponse getConfig(Long id) {
        DhrImMeetingDetailRuleResponse meetingDetailRuleResponse = new DhrImMeetingDetailRuleResponse();
        DhrMtTemplateInfoResponse templateInfoResponse = dhrMaActivityInfoService.getTemplateInfoByMaId(id);
        meetingDetailRuleResponse.setTemplateInfoResponse(templateInfoResponse);
        List<DhrMaConvertRuleResponse> convertRuleResponses = dhrMaConvertRuleService.getListByMaId(id);
        meetingDetailRuleResponse.setDhrMaConvertRuleResponseList(convertRuleResponses);
        return meetingDetailRuleResponse;
    }

    @Override
    public DhrImMeetingEmpResponse getGroupAllEmpList(Page page,
                                                      DhrImMeetingEmpRequest request,
                                                      BaseOrder order) {
        DhrImMeetingEmpResponse meetingEmpResponse = new DhrImMeetingEmpResponse();
        for (int i = 0; i <= DhrMaConstant.inventoryGroup.GROUP9; i++) {
            request.setGroup(i);
            List<DhrImMeetingDetailInfoResponse> list = dhrImMeetingDetailInfoMapper.getGroupEmpList(page, request, this.adjustOrder(order));
            ResponsePage<DhrImMeetingDetailInfoResponse> responsePage = new ResponsePage<>(page, list);
            dhrImMeetingEmpResponseAddGroup(meetingEmpResponse, responsePage, i);
        }
        return meetingEmpResponse;
    }

    @Override
    public ResponsePage<DhrImMeetingDetailInfoResponse> getGroupEmpList(Page page, DhrImMeetingEmpRequest request, BaseOrder order) {
        List<DhrImMeetingDetailInfoResponse> list = dhrImMeetingDetailInfoMapper.getGroupEmpList(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addNotEvaluateEmp(DhrImMeetingEmpNotRequest request) {
        // 添加人员到测评对象中
        Long maId = request.getMaId();
        List<String> empCodeList = request.getEmpCodeList();
        // 添加
        Request<EmployeeFromPersonnelWarehouseRequest> employeeRequest = new Request<>();
        EmployeeFromPersonnelWarehouseRequest employeeFromPersonnelWarehouseRequest = new EmployeeFromPersonnelWarehouseRequest();
        employeeFromPersonnelWarehouseRequest.setEmpCodes(empCodeList);
        employeeFromPersonnelWarehouseRequest.setEngageStatus(3);
        employeeRequest.setParam(employeeFromPersonnelWarehouseRequest);
        List<EmployeeFromPersonnelWarehouseResponse> employees = employeeFromPersonnelWarehouseFeignInterface
                .listEmp(null, 1L, -1L, employeeRequest).getData().getDataList();
        addNotInventoryObjectBatch(maId, employees);
        // 添加盘点会议详情主表数据
        return addNotMeetingDetailBatch(maId, employees);
    }

    /**
     * 批量添加未测评对象
     *
     * @param maId
     * @param employees
     * @return
     */
    private Boolean addNotInventoryObjectBatch(Long maId, List<EmployeeFromPersonnelWarehouseResponse> employees) {
        List<DhrMaInventoryObject> list = new ArrayList<>();
        employees.forEach(employee -> {
            DhrMaInventoryObject inventoryObject = new DhrMaInventoryObject();
            inventoryObject.setMaId(maId);
            inventoryObject.setEmpCode(employee.getEmpCode());
            inventoryObject.setEmpName(employee.getFullname());
            inventoryObject.setEmpPositionCode(employee.getPositionCode());
            inventoryObject.setEmpPositionName(employee.getPositionDesc());
            inventoryObject.setOaCode(employee.getEmpCode());
            inventoryObject.setEmpOrgCode(employee.getOrgCode());
            inventoryObject.setEmpOrgFullPath(employee.getOrgPathName());
            inventoryObject.setEmpOrgCodeFullPath(employee.getOrgPath());
            inventoryObject.setEmpStatus(employee.getEngageStatus());
            inventoryObject.setHireDate(DateUtil.parseDate(employee.getHireDate()));
            inventoryObject.setEmpSequenceCode(employee.getSeqCode());
            inventoryObject.setEmpSequenceName(employee.getSeqName());
            // 设置状态未评估、不参与初始分布计算
            inventoryObject.setEvaluationStatus(false);
            inventoryObject.setIsInitCalculate(false);
            list.add(inventoryObject);
        });
        // 校验重复
        Set<DhrMaInventoryObject> dhrMaInventoryObjects = CheckModelUtil.checkSameDateBatch(dhrMaInventoryObjectMapper, list, DhrMaInventoryObject::getMaId, DhrMaInventoryObject::getEmpCode);
        if (CollUtil.isNotEmpty(dhrMaInventoryObjects)) {
            dhrMaInventoryObjects.forEach(item -> {
                throw new CommRunException("dhr.talent.inventory.object.exists");
            });
        }
        return dhrMaInventoryObjectService.saveBatch(list);
    }

    /**
     * 添加人员到盘点会议详情中
     *
     * @param maId
     * @param employees
     * @return
     */
    private Boolean addNotMeetingDetailBatch(Long maId, List<EmployeeFromPersonnelWarehouseResponse> employees) {
        List<DhrImMeetingDetailInfo> list = new ArrayList<>();
        employees.forEach(employee -> {
            DhrImMeetingDetailInfo meetingDetailInfo = new DhrImMeetingDetailInfo();
            meetingDetailInfo.setMaId(maId);
            meetingDetailInfo.setEmpCode(employee.getEmpCode());
            meetingDetailInfo.setPalacePosition(DhrMaConstant.inventoryGroup.GROUP0);
            list.add(meetingDetailInfo);
        });
        return this.saveBatch(list);
    }

    /**
     * 会议详情内容添加分组内容信息
     *
     * @param meetingEmpResponse
     * @param responsePage
     * @param group
     */
    private void dhrImMeetingEmpResponseAddGroup(DhrImMeetingEmpResponse meetingEmpResponse, ResponsePage<DhrImMeetingDetailInfoResponse> responsePage, Integer group) {
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP0)) {
            meetingEmpResponse.setGroup0(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP1)) {
            meetingEmpResponse.setGroup1(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP2)) {
            meetingEmpResponse.setGroup2(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP3)) {
            meetingEmpResponse.setGroup3(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP4)) {
            meetingEmpResponse.setGroup4(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP5)) {
            meetingEmpResponse.setGroup5(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP6)) {
            meetingEmpResponse.setGroup6(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP7)) {
            meetingEmpResponse.setGroup7(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP8)) {
            meetingEmpResponse.setGroup8(responsePage);
        }
        if (group.equals(DhrMaConstant.inventoryGroup.GROUP9)) {
            meetingEmpResponse.setGroup9(responsePage);
        }
    }


    @Override
    public Boolean saveData(DhrImMeetingDetailInfoRequest request) {
        DhrImMeetingDetailInfo dhrImMeetingDetailInfo = new DhrImMeetingDetailInfo();
        BeanUtils.copyProperties(request, dhrImMeetingDetailInfo);
        return this.insert(dhrImMeetingDetailInfo);
    }

    @Override
    public Boolean saveEvaluateEmp(List<DhrImMeetingEmpSaveRequest> requestList) {
        // 更新盘点对象宫格位置、绩效/能力
        requestList.forEach(item -> {
            LambdaUpdateWrapper<DhrImMeetingDetailInfo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(DhrImMeetingDetailInfo::getMaId, item.getMaId());
            wrapper.eq(DhrImMeetingDetailInfo::getEmpCode, item.getEmpCode());
            DhrImMeetingDetailInfo dhrImMeetingDetailInfo = BeanUtil.copyProperties(item, DhrImMeetingDetailInfo.class);
            dhrImMeetingDetailInfo.setPalacePosition(item.getGroup());
            dhrImMeetingDetailInfoMapper.update(dhrImMeetingDetailInfo, wrapper);
            // 保存调整记录
            DhrImChangeHistory imChangeHistory = BeanUtil.copyProperties(dhrImMeetingDetailInfo, DhrImChangeHistory.class);
            dhrImChangeHistoryService.saveData(imChangeHistory);
        });
        return true;
    }

    @Override
    public String submitMeetingDetailCheck(Long maId) {
        StringBuffer tipsBuf = new StringBuffer();
        LambdaUpdateWrapper<DhrImMeetingDetailInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DhrImMeetingDetailInfo::getMaId, maId);
        wrapper.eq(DhrImMeetingDetailInfo::getPalacePosition, DhrMaConstant.inventoryGroup.GROUP0);
        Long total = dhrImMeetingDetailInfoMapper.selectCount(wrapper);
        if (total > 0) {
            tipsBuf.append(MessageUtils.toLocale("dhr.talent.inventory.unevaluated.exists"));
        }
        return tipsBuf.toString();
    }

    @Override
    public Boolean submitMeetingDetail(Long maId) {
        DhrMaActivityInfo maActivityInfo = new DhrMaActivityInfo();
        maActivityInfo.setId(maId);
        maActivityInfo.setMaStatus(DhrMaConstant.MaStatus.PDWC);
        maActivityInfo.setFinishTime(new Date());
        return dhrMaActivityInfoService.update(maActivityInfo);
    }

    @Override
    public Integer countNotInventoryEmp(Long maId) {
        LambdaQueryWrapper<DhrImMeetingDetailInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(DhrImMeetingDetailInfo::getId);
        wrapper.eq(DhrImMeetingDetailInfo::getMaId, maId);
        wrapper.eq(DhrImMeetingDetailInfo::getPalacePosition, DhrMaConstant.inventoryGroup.GROUP0);
        Long count = this.getBaseMapper().selectCount(wrapper);
        return count.intValue();
    }

    @Override
    public Boolean updateData(DhrImMeetingDetailInfoRequest request) {
        DhrImMeetingDetailInfo dhrImMeetingDetailInfo = new DhrImMeetingDetailInfo();
        BeanUtils.copyProperties(request, dhrImMeetingDetailInfo);
        return this.update(dhrImMeetingDetailInfo);
    }

    @Override
    public Boolean batchDelete(List<Long> ids) {
        return this.delete(ids);
    }

    @Override
    public void export(DhrImMeetingEmpRequest request, BaseOrder order) {
        DhrMaActivityInfoResponse dhrMaActivityInfoResponse = dhrMaActivityInfoService.get(request.getMaId());
        String maName = dhrMaActivityInfoResponse.getMaName();
        List<DhrImMeetingDetailInfoResponse> responseList = dhrImMeetingDetailInfoMapper.getAllGroupEmpList(new Page<>(1L, -1L), request, this.adjustOrder(order));
        List<DhrImMeetingDetailInfoDTO> list = BeanUtil.copyToList(responseList, DhrImMeetingDetailInfoDTO.class);
        // 导出excel
        ExcelSheetData<DhrImMeetingDetailInfoDTO> excelSheetData = new ExcelSheetData<>(DhrImMeetingDetailInfoDTO.class, list);
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        String nowFormat = DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
        excelParserTemplate.export(maName + "_" + nowFormat + ".xlsx", CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), null);
    }

    @Override
    public Boolean saveInventoryEvaluation(DhrImMeetingDetailInfoRequest request) {
        DhrImMeetingDetailInfo dhrImMeetingDetailInfo = BeanUtil.copyProperties(request, DhrImMeetingDetailInfo.class);
        return this.updateById(dhrImMeetingDetailInfo);
    }

    @Override
    public List<DhrImMeetingDetailHistoryResponse> getLastThreeTimesDetail(String empCode, List<String> activityTypeList) {
        return dhrImMeetingDetailInfoMapper.getLastThreeTimesDetail(empCode, activityTypeList, RECENT_TIMES);
    }

    @Override
    public ResponsePage<DhrImMeetingHistoryResponse> getLastThreeTimesList(Page page, DhrImMeetingHistoryRequest request, BaseOrder order) {
        // 查询盘点会议中的员工信息
        List<DhrImMeetingHistoryResponse> inventoryObjectList = dhrImMeetingDetailInfoMapper.getInventoryObjectList(page, request, this.adjustOrder(order));
        // 获取近三次盘点会议记录
        inventoryObjectList.forEach(inventoryObject -> {
            List<DhrImMeetingDetailHistoryResponse> lastThreeTimesDetail = getLastThreeTimesDetail(inventoryObject.getEmpCode(), request.getActivityTypeList());
            setRecentPalacePosition(inventoryObject, lastThreeTimesDetail);
        });
        return new ResponsePage<>(page, inventoryObjectList);
    }

    @Override
    public Integer convertPalacePosition(String performance, String abilityOrPotential) {
        if (DhrMaConstant.Performance.LOW.equals(performance) && DhrMaConstant.AbilityOrPotential.LOW.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP1;
        } else if (DhrMaConstant.Performance.LOW.equals(performance) && DhrMaConstant.AbilityOrPotential.MEDIUM.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP2;
        } else if (DhrMaConstant.Performance.LOW.equals(performance) && DhrMaConstant.AbilityOrPotential.HIGH.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP3;
        } else if (DhrMaConstant.Performance.MEDIUM.equals(performance) && DhrMaConstant.AbilityOrPotential.LOW.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP4;
        } else if (DhrMaConstant.Performance.MEDIUM.equals(performance) && DhrMaConstant.AbilityOrPotential.MEDIUM.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP5;
        } else if (DhrMaConstant.Performance.MEDIUM.equals(performance) && DhrMaConstant.AbilityOrPotential.HIGH.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP6;
        } else if (DhrMaConstant.Performance.HIGH.equals(performance) && DhrMaConstant.AbilityOrPotential.LOW.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP7;
        } else if (DhrMaConstant.Performance.HIGH.equals(performance) && DhrMaConstant.AbilityOrPotential.MEDIUM.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP8;
        } else if (DhrMaConstant.Performance.HIGH.equals(performance) && DhrMaConstant.AbilityOrPotential.HIGH.equals(abilityOrPotential)) {
            return DhrMaConstant.inventoryGroup.GROUP9;
        }
        return DhrMaConstant.inventoryGroup.GROUP0;
    }

    /**
     * 组装最近几次宫格位置(转换到列字段上)
     *
     * @param inventoryObject
     * @param lastThreeTimesDetail
     * @return
     */
    private void setRecentPalacePosition(DhrImMeetingHistoryResponse inventoryObject, List<DhrImMeetingDetailHistoryResponse> lastThreeTimesDetail) {
        if (CollUtil.isNotEmpty(lastThreeTimesDetail)) {
            for (int i = 0; i < lastThreeTimesDetail.size(); i++) {
                if (i == 0) {
                    DhrImMeetingDetailHistoryResponse oneHistoryResponse = lastThreeTimesDetail.get(i);
                    if (oneHistoryResponse != null) {
                        inventoryObject.setRecentOnePalacePosition(oneHistoryResponse.getPalacePosition());
                        inventoryObject.setRecentOnePalacePositionName(oneHistoryResponse.getPalacePositionName());
                    }
                } else if (i == 1) {
                    DhrImMeetingDetailHistoryResponse twoHistoryResponse = lastThreeTimesDetail.get(i);
                    if (twoHistoryResponse != null) {
                        inventoryObject.setRecentTwoPalacePosition(twoHistoryResponse.getPalacePosition());
                        inventoryObject.setRecentTwoPalacePositionName(twoHistoryResponse.getPalacePositionName());
                    }
                } else if (i == 2) {
                    DhrImMeetingDetailHistoryResponse threeHistoryResponse = lastThreeTimesDetail.get(i);
                    if (threeHistoryResponse != null) {
                        inventoryObject.setRecentThreePalacePosition(threeHistoryResponse.getPalacePosition());
                        inventoryObject.setRecentThreePalacePositionName(threeHistoryResponse.getPalacePositionName());
                    }
                }
            }

        }
    }
}

