package com.deloitte.dhr.talent.module.evaluation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.bpm.constant.WebConstant;
import com.deloitte.bpm.dto.admin.ProcessScopeQueryRequest;
import com.deloitte.bpm.dto.process.ProcessFormRequest;
import com.deloitte.bpm.provider.ProcessInstancesProvider;
import com.deloitte.bpm.provider.ProcessScopeProvider;
import com.deloitte.bpm.provider.TasksProvider;
import com.deloitte.bpm.vo.ResultVO;
import com.deloitte.bpm.vo.admin.ProcessScopeVO;
import com.deloitte.bpm.vo.web.ProcessRuInfo;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.MessageUtils;
import com.deloitte.dhr.common.base.utils.StrUtils;
import com.deloitte.dhr.mda.module.base.pojo.EmployeeBasicInfoResp;
import com.deloitte.dhr.report.pojo.entity.ReportHtmlTpl;
import com.deloitte.dhr.report.template.ReportTemplate;
import com.deloitte.dhr.talent.constant.TalentEnumCodeConstant;
import com.deloitte.dhr.talent.module.evaluation.constant.DhrEaConstant;
import com.deloitte.dhr.talent.module.evaluation.domain.*;
import com.deloitte.dhr.talent.module.evaluation.mapper.*;
import com.deloitte.dhr.talent.module.evaluation.pojo.*;
import com.deloitte.dhr.talent.module.evaluation.service.*;
import com.deloitte.dhr.talent.util.*;
import com.deloitte.dhr.utility.api.CommonFileDfsInterface;
import com.deloitte.dhr.utility.api.dto.email.EmailParamDto;
import com.deloitte.dhr.utility.api.dto.file.FileRequestDto;
import com.deloitte.dhr.utility.api.dto.file.FileResponseDto;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * Comment：dhr_ea_activity_info360测评 - 测评活动管理 - 测评活动主表
 *
 * <AUTHOR>
 * @date 2022-09-14
 */
@Service
@Slf4j
@Data
public class DhrEaActivityInfoServiceImpl extends SuperServiceImpl<DhrEaActivityInfoMapper, DhrEaActivityInfo> implements DhrEaActivityInfoService {
    @Resource
    private DhrEaActivityInfoMapper dhrEaActivityInfoMapper;

    @Resource
    private DhrEaOrgRelService dhrEaOrgRelService;

    @Resource
    private TalentFeignMethodUtil talentFeignMethodUtil;

    @Resource
    private ProcessInstancesProvider processInstancesProvider;

    @Resource
    private TasksProvider tasksProvider;

    @Resource
    private ProcessScopeProvider processScopeProvider;

    @Resource
    private DhrEaEvaluationPathService dhrEaEvaluationPathService;

    @Resource
    private DhrEaEvaluationObjectService dhrEaEvaluationObjectService;
    @Resource
    private DhrEaObjReviewerRelMapper dhrEaObjReviewerRelMapper;
    @Resource
    private DhrEaEvaluationTransferMapper dhrEaEvaluationTransferMapper;

    @Resource
    private DhrEaEvaluationReviewerService dhrEaEvaluationReviewerService;

    @Resource
    private DhrEaEvaluationReviewerTempService dhrEaEvaluationReviewerTempService;

    @Autowired
    private DhrQdQuestionnaireInfoService dhrQdQuestionnaireInfoService;

    @Resource
    private DhrEaEvaluationStatisticsService dhrEaEvaluationStatisticsService;

    @Resource
    private DhrEaObjReviewerRelService dhrEaObjReviewerRelService;

    @Autowired
    private DhrEaEvaluatorAsyncService dhrEaEvaluatorAsyncService;

    @Autowired
    private DhrEaReportTeamHistoryService dhrEaReportTeamHistoryService;

    @Autowired
    private DhrRsRatingScaleInfoService dhrRsRatingScaleInfoService;

    @Autowired
    private DhrEaEvaluationWeightService dhrEaEvaluationWeightService;

    @Autowired
    private DhrEaReportAbilityScoreService dhrEaReportAbilityScoreService;

    @Autowired
    private DhrEaReportTotalScoreService dhrEaReportTotalScoreService;

    @Autowired
    private DhrEaReportDimenScoreService dhrEaReportDimenScoreService;

    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Resource
    private DhrEaEvaluationWeightMapper dhrEaEvaluationWeightMapper;
    @Resource
    private DhrEaEvaluationObjectMapper dhrEaEvaluationObjectMapper;
    @Autowired
    private DhrEaReportPersonalHistoryService dhrEaReportPersonalHistoryService;

    @Autowired
    private CommonFileDfsInterface commonFileDfsInterface;

    @Resource
    private ReportTemplate reportTemplate;

    @Value("${dhr.reportUrl}")
    private String reportUrl;


    private final Lock lock = new ReentrantLock();

    /**
     * 团队报告保留小数位数
     */
    private final Integer TEAM_REPORT_SCALE = 2;


    /**
     * 业务编码分隔符号
     */
    private static final String BUSINESS_NO_DELIMITER = "_";

    /**
     * 异步执行线程池
     */
    private final ExecutorService cachedThreadPool = new ThreadPoolExecutor(10, 50, 300, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    /**
     * 补偿任务执行线程池
     */
    private final ExecutorService retryThreadPool = new ThreadPoolExecutor(10, 50, 300, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    @Value("${360Evaluation.isAsynchronous:true}")
    private Boolean isAsynchronous;

    @Value("${360Evaluation.download-url}")
    private String downloadFileUrl;

    @Override
    public ResponsePage findPageListResp(Page page, DhrEaActivityInfoListRequest request, BaseOrder order) {
        List<DhrEaActivityInfoResponse> dataList = this.dhrEaActivityInfoMapper.findListResp(page, request, this.adjustOrder(order));
        if (CollUtil.isNotEmpty(dataList)) {
            for (DhrEaActivityInfoResponse item : dataList) {
                // 填充组织适用范围名称
                List<DhrEaOrgRel> list = dhrEaOrgRelService.getList(item.getId());
                List<String> orgCodes = list.stream().map(DhrEaOrgRel::getOrgCode).collect(Collectors.toList());
                // 加入所属组织编码
                item.setEaOrgCodes(orgCodes.toArray(new String[orgCodes.size()]));
                orgCodes.add(item.getBelongOrgCode());
                Map<String, String> orgNameMap = talentFeignMethodUtil.findByOrgCodes(orgCodes);
                List<String> eaOrgNames = new ArrayList<>();
                for (Map.Entry<String, String> entry : orgNameMap.entrySet()) {
                    eaOrgNames.add(entry.getValue());
                }
                // 填充所属组织名称
                item.setBelongOrgCodeName(orgNameMap.get(item.getBelongOrgCode()));
                item.setEaOrgCodeNames(eaOrgNames.toArray(new String[eaOrgNames.size()]));
            }
        }
        return new ResponsePage(page, dataList);
    }

    @Override
    public List<DhrEaActivityInfoResponse> findList(DhrEaActivityInfoListRequest request) {
        return dhrEaActivityInfoMapper.findList(request);
    }


    @Override
    public DhrEaActivityInfoResponse getDetailById(Long id) {
        DhrEaActivityInfo dhrEaActivityInfo = getById(id);
        DhrEaActivityInfoResponse dhrEaActivityInfoResponse = BeanUtil.copyProperties(dhrEaActivityInfo, DhrEaActivityInfoResponse.class);
        if (dhrEaActivityInfo != null) {
            // 填充组织适用范围
            List<DhrEaOrgRel> list = dhrEaOrgRelService.getList(dhrEaActivityInfoResponse.getId());
            List<String> orgCodes = list.stream().map(DhrEaOrgRel::getOrgCode).distinct().collect(Collectors.toList());
            dhrEaActivityInfoResponse.setEaOrgCodes(orgCodes.toArray(new String[orgCodes.size()]));
            // 加入所属组织编码（批量转义）
            orgCodes.add(dhrEaActivityInfo.getBelongOrgCode());
            Map<String, String> orgNameMap = talentFeignMethodUtil.findByOrgCodes(orgCodes);
            List<String> eaOrgNames = new ArrayList<>();
            for (Map.Entry<String, String> entry : orgNameMap.entrySet()) {
                eaOrgNames.add(entry.getValue());
            }
            // 填充所属组织名称
            dhrEaActivityInfoResponse.setBelongOrgCodeName(orgNameMap.get(dhrEaActivityInfo.getBelongOrgCode()));
            dhrEaActivityInfoResponse.setEaOrgCodeNames(eaOrgNames.toArray(new String[eaOrgNames.size()]));
            // 填充问卷名称
            DhrQdQuestionnaireInfoResponse qdQuestionnaireInfoResponse = dhrQdQuestionnaireInfoService.getDetailById(dhrEaActivityInfo.getQdId());
            dhrEaActivityInfoResponse.setQdName(qdQuestionnaireInfoResponse.getQdName());
            //流程启用禁用
            DhrEaEvaluationPathResponse dhrEaEvaluationPathResponse = dhrEaEvaluationPathService.findByEaId(id);
            dhrEaActivityInfoResponse.setProcessEnabled(dhrEaEvaluationPathResponse == null ? null : dhrEaEvaluationPathResponse.getEnabled());
        }
        return dhrEaActivityInfoResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DhrEaActivityInfoResponse insert(DhrEaActivityInfoRequest dhrEaActivityInfoRequest) {
        DhrEaActivityInfo dhrEaActivityInfo = BeanUtil.copyProperties(dhrEaActivityInfoRequest, DhrEaActivityInfo.class);
        // 基于“所属组织”校验“活动名称”是否已有同名记录存在
        boolean isSame = CheckModelUtil.checkSameDate(dhrEaActivityInfoMapper, dhrEaActivityInfo,
                DhrEaActivityInfo::getBelongOrgCode, DhrEaActivityInfo::getEaName);
        if (isSame) {
            throw new CommRunException("dhr.mdb.db.error.isExistsNotSave");
        }
        String fullName = TalentCommonUtil.fillLoginEmpName();
        boolean insertResult;
        lock.lock();
        try {
            // 生成编码
            dhrEaActivityInfo.setEaCode(serialNumberUtil.getSerialNumber(DhrEaConstant.SerialNumGroup.HD));
            // 状态草稿
            dhrEaActivityInfo.setEaStatus(DhrEaConstant.ActivityInfo.EA_STATUS_CG);
            // 统计冗余初始值
            dhrEaActivityInfo.setEvaluationCount(0);
            dhrEaActivityInfo.setEvaluationUnfinishedCount(0);
            dhrEaActivityInfo.setEvaluationFinishedCount(0);
            dhrEaActivityInfo.setEvaluationRate(BigDecimal.valueOf(0));
            dhrEaActivityInfo.setCreateName(fullName);
            insertResult = insert(dhrEaActivityInfo);
        } finally {
            lock.unlock();
        }
        if (insertResult) {
            // 保存适用组织范围
            dhrEaOrgRelService.saveBatch(dhrEaActivityInfoRequest.getEaOrgCodes(), dhrEaActivityInfo.getId());
            return BeanUtil.copyProperties(dhrEaActivityInfo, DhrEaActivityInfoResponse.class);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DhrEaActivityInfoResponse update(DhrEaActivityInfoRequest dhrEaActivityInfoRequest) {
        DhrEaActivityInfo dhrEaActivityInfo = BeanUtil.copyProperties(dhrEaActivityInfoRequest, DhrEaActivityInfo.class);
        // 基于“所属组织”校验“活动名称”是否已有同名记录存在
        boolean isSame = CheckModelUtil.checkSameDate(dhrEaActivityInfoMapper, true, dhrEaActivityInfo,
                DhrEaActivityInfo::getBelongOrgCode, DhrEaActivityInfo::getEaName);
        if (isSame) {
            throw new CommRunException("dhr.mdb.db.error.isExistsNotSave");
        }
        String fullName = TalentCommonUtil.fillLoginEmpName();
        dhrEaActivityInfo.setUpdateName(fullName);
        boolean insertResult = update(dhrEaActivityInfo);
        if (insertResult) {
            // 修改适用组织范围
            dhrEaOrgRelService.updateBatch(dhrEaActivityInfoRequest.getEaOrgCodes(), dhrEaActivityInfo.getId());
            return BeanUtil.copyProperties(dhrEaActivityInfo, DhrEaActivityInfoResponse.class);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(List<Long> ids) {
        // 检查测评活动状态
        for (Long id : ids) {
            DhrEaActivityInfo dhrEaActivityInfo = getById(id);
            if (!DhrEaConstant.ActivityInfo.EA_STATUS_CG.equals(dhrEaActivityInfo.getEaStatus())) {
                throw new CommRunException("dhr.mdb.db.error.isNonDraftNotDel");
            }
            // 清理适用组织范围关系
            dhrEaOrgRelService.deleteByEaId(id);
        }
        return delete(ids);
    }

    @Override
    public ResponsePage progressList(Page page, DhrEaActivityInfoRequest request) {
        List list = dhrEaActivityInfoMapper.progressList(page, request);
        return new ResponsePage(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean start(Long id) {
        // 获取测评活动路径配置的流程信息
        DhrEaEvaluationPathResponse pathResponse = dhrEaEvaluationPathService.findByEaId(id);
        DhrEaActivityInfo activityInfo = this.getById(id);
        // 如果开启自主选评论者且未草稿状态，则发起流程
        if (DhrEaConstant.ENABLE.equals(pathResponse.getEnabled()) && DhrEaConstant.ActivityInfo.EA_STATUS_CG.equals(activityInfo.getEaStatus())) {
            // 获取测评对象列表
            LambdaQueryWrapper<DhrEaEvaluationObject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DhrEaEvaluationObject::getEaId, id);
            List<DhrEaEvaluationObject> dhrEaEvaluationObjects = dhrEaEvaluationObjectService.getBaseMapper().selectList(queryWrapper);
            List<DhrEaEvaluationObjectResponse> evaluationObjectList = BeanUtil.copyToList(dhrEaEvaluationObjects, DhrEaEvaluationObjectResponse.class);
            if (CollUtil.isEmpty(evaluationObjectList)) {
                throw new CommRunException("dhr.talent.error.activity.least.one.add.object");
            }
            // 拷贝该活动评价对象、评价者、评价对象与评价者关系到审批临时表中(批量)
            dhrEaEvaluationReviewerTempService.copy2Draft(id, dhrEaEvaluationObjects);
            // 获取流程发起所需其他参数
            ProcessScopeQueryRequest processScopeQueryRequest = new ProcessScopeQueryRequest();
            processScopeQueryRequest.setProcessId(pathResponse.getProcessId());
            ProcessScopeVO processScopeVO = remoteBpmHandle(processScopeProvider.selectOne(processScopeQueryRequest));
            SecurityContext context = SecurityContextHolder.getContext();
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (processScopeVO != null && CollUtil.isNotEmpty(evaluationObjectList)) {
                String starter = TalentCommonUtil.fillLoginEmpCode();
                String starterName = TalentCommonUtil.fillLoginEmpName();
                for (DhrEaEvaluationObjectResponse evaluationObject : evaluationObjectList) {
                    if (isAsynchronous) {
                        cachedThreadPool.execute(new Runnable() {
                            @Override
                            public void run() {
                                // 保证feign调用正常
                                SecurityContextHolder.setContext(context);
                                RequestContextHolder.setRequestAttributes(requestAttributes);
                                // 启动流程
                                getThisBean().startBpm(processScopeVO, evaluationObject, id, starter, starterName,
                                        activityInfo.getEaName() + "-" + evaluationObject.getEmpName() + "-" + processScopeVO.getProcessName());
                            }
                        });
                    } else {
                        startBpm(processScopeVO, evaluationObject, id, starter, starterName, processScopeVO.getProcessName());
                    }
                }
            }
            // 流程发起补偿任务
            if (isAsynchronous) {
                retryThreadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        // 保证feign调用正常
                        SecurityContextHolder.setContext(context);
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        // 默认重试3次: 30秒，60秒，120秒...
                        retryStartBpm(id, 3, 30000L, 2);
                    }
                });
            }
        }
        // 更新测评活动状态
        updateStatus(id, DhrEaConstant.ActivityInfo.EA_STATUS_QY);
        return true;
    }

    @Override
    public Boolean restart(Long id, DhrEaActivityRestartRequest restartRequest) {
        SecurityContext context = SecurityContextHolder.getContext();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        retryThreadPool.execute(new Runnable() {
            @Override
            public void run() {
                // 保证feign调用正常
                SecurityContextHolder.setContext(context);
                RequestContextHolder.setRequestAttributes(requestAttributes);
                // 执行重试
                retryStartBpm(id, restartRequest.getTimes(), restartRequest.getSleepMillis(), restartRequest.getMultiple());
            }
        });
        return true;
    }

    /**
     * 流程补偿任务
     *
     * @param id          活动ID
     * @param times       重试次数
     * @param sleepMillis 间隔毫秒数
     * @param multiple    间隔延时阶梯
     */
    private void retryStartBpm(Long id, Integer times, Long sleepMillis, Integer multiple) {
        ProcessScopeVO processScopeVO = null;
        while (times > 0) {
            // 延时处理
            try {
                Thread.sleep(sleepMillis);
                sleepMillis = sleepMillis * multiple;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
            // 活动获取测评对象列表
            List<DhrEaEvaluationObjectResponse> evaluationObjectList = dhrEaEvaluationObjectService.getListByEaId(id);
            for (DhrEaEvaluationObjectResponse evaluationObject : evaluationObjectList) {
                // 如果状态为失败，则进行补偿
                if (DhrEaConstant.ApprovalStatus.FAIL.equals(evaluationObject.getProcessStatus())) {
                    // 判断流程是否发起成功:通过业务编码查询
                    String businessNo = evaluationObject.getEaId() + BUSINESS_NO_DELIMITER + evaluationObject.getEmpCode()
                            + BUSINESS_NO_DELIMITER + evaluationObject.getEmpPositionCode();
                    ResultVO<List<String>> result = tasksProvider.getTaskByBusinessNo(null, businessNo);
                    if (WebConstant.RESULT_CODE_200.equals(result.getCode())) {
                        List<String> procInstIds = result.getData();
                        if (CollUtil.isNotEmpty(procInstIds)) {
                            String procInstId = procInstIds.get(0);
                            // 如果流程任务存在，更新超时引起的错误记录
                            updateEvaluationObjectCurrentStage(evaluationObject.getId(), DhrEaConstant.ApprovalCurrentStage.SELF_SELECTED, procInstId);
                        } else {
                            if (processScopeVO == null) {
                                // 如果流程任务不存在，重试发起流程
                                DhrEaEvaluationPathResponse pathResponse = dhrEaEvaluationPathService.findByEaId(id);
                                // 获取流程发起所需其他参数
                                ProcessScopeQueryRequest processScopeQueryRequest = new ProcessScopeQueryRequest();
                                processScopeQueryRequest.setProcessId(pathResponse.getProcessId());
                                processScopeVO = remoteBpmHandle(processScopeProvider.selectOne(processScopeQueryRequest));
                            }
                            String starter = TalentCommonUtil.fillLoginEmpCode();
                            String starterName = TalentCommonUtil.fillLoginEmpName();
                            if (processScopeVO != null) {
                                startBpm(processScopeVO, evaluationObject, id, starter, starterName, processScopeVO.getProcessName());
                            }
                        }
                    }
                }
            }
            // 重试次数递减
            times--;
        }

    }

    @Override
    public List<String> startCheck(Long id) {
        List<String> messageList = new ArrayList<>();
        List<Map<String, Object>> evaluationRelStatistics = dhrEaObjReviewerRelMapper.getEvaluationRelStatistics(id);
        DhrEaEvaluationPathResponse pathResponse = dhrEaEvaluationPathService.findByEaId(id);
        // 是否启用“自助选择评价者”
        boolean pathEnabled = DhrEaConstant.ENABLE.equals(pathResponse.getEnabled());
        boolean selfTypeEnabled = false;
        List<DhrEaEvaluationWeightResponse> weightResponseList = dhrEaEvaluationWeightService.findListByEaId(id);
        for (DhrEaEvaluationWeightResponse weightResponse : weightResponseList) {
            if (DhrEaConstant.EvaluationType.BR_TYPE.equals(weightResponse.getEvaluationType()) && DhrEaConstant.ENABLE.equals(weightResponse.getEnabled())) {
                selfTypeEnabled = true;
                break;
            }
        }
        int i = 0;
        do {
            Long total = 0L;
            if (CollUtil.isNotEmpty(evaluationRelStatistics)) {
                Map<String, Object> relMap = evaluationRelStatistics.get(i);
                total = (Long) relMap.get("total");
            }

            // 强校验:启用本人评价者类别 & 启用“自助选择评价者”
            if (pathEnabled && selfTypeEnabled) {
                if (total < 1) {
                    throw new CommRunException("dhr.talent.error.activity.add.least.one.reviewer");
                }
            }
            // 软校验:启用“自助选择评价者” & 未启用本人评价者类别
            if (pathEnabled && !selfTypeEnabled) {
                if (total < 1) {
                    messageList.add(MessageUtils.toLocale("dhr.talent.error.activity.add.least.any.reviewer"));
                    break;
                }
            }
            // 强校验:未启用“自助选择评价者” & 启用本人评价者类别
            if (!pathEnabled && selfTypeEnabled) {
                if (total < 2) {
                    throw new CommRunException("dhr.talent.error.activity.add.least.two.reviewer");
                }
            }
            // 强校验:未启用“自助选择评价者” & 未启用本人评价者类别
            if (!pathEnabled && !selfTypeEnabled) {
                if (total < 1) {
                    throw new CommRunException("dhr.talent.error.activity.add.least.one.reviewer");
                }
            }
            ++i;
        } while (i < evaluationRelStatistics.size());
        return messageList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startBpm(ProcessScopeVO processScopeVO, DhrEaEvaluationObjectResponse evaluationObject, Long eaId,
                            String starter, String starterName, String processName) {
        try {
            List<String> evaluationObjectIds = Lists.newArrayList(evaluationObject.getEmpCode());
            String businessNo = evaluationObject.getEaId() + BUSINESS_NO_DELIMITER + evaluationObject.getEmpCode()
                    + BUSINESS_NO_DELIMITER + evaluationObject.getEmpPositionCode();
            ResultVO<ProcessRuInfo> result = startBpmProcess(processScopeVO, starter, starterName,
                    evaluationObject.getEmpCode(), evaluationObject.getEmpName(), businessNo, processName, evaluationObjectIds);
            if (WebConstant.RESULT_CODE_200.equals(result.getCode())) {
                // 发起成功，更新评价对象当前审批阶段：选择测评者（编码：YG）
                ProcessRuInfo processRuInfo = result.getData();
                updateEvaluationObjectCurrentStage(evaluationObject.getId(), DhrEaConstant.ApprovalCurrentStage.SELF_SELECTED, processRuInfo.getProcessInstanceId());
            } else {
                // 记录失败状态
                updateEvaluationObjectApprovalStatusFail(evaluationObject.getId(), result.toString());
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            // 记录失败状态
            updateEvaluationObjectApprovalStatusFail(evaluationObject.getId(), e.getMessage());
        }
        return false;
    }

    @Override
    public Boolean saveEaActivityInfo(Long id) {
        return updateStatus(id, DhrEaConstant.ActivityInfo.EA_STATUS_CG);
    }

    @Override
    public Boolean stopEaActivityInfo(Long id) {
        return updateStatus(id, DhrEaConstant.ActivityInfo.EA_STATUS_TY);
    }

    @Override
    public DhrEaEvaluationEmpCountResponse empCount(Long id) {
        //统计评价者和评价对象
        LambdaQueryWrapper<DhrEaObjReviewerRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DhrEaObjReviewerRel::getEaId, id);
        List<DhrEaObjReviewerRel> reviewerRelList = dhrEaObjReviewerRelMapper.selectList(queryWrapper);
        //评价者人数
        long evaluationReviewerNumber = reviewerRelList.stream().map(DhrEaObjReviewerRel::getEvaluationReviewerCode).distinct().count();
        //评价对象人数
        LambdaQueryWrapper<DhrEaEvaluationObject> objectQueryWrapper = new LambdaQueryWrapper<>();
        objectQueryWrapper.eq(DhrEaEvaluationObject::getEaId, id);
        Long evaluationObjectNumber = dhrEaEvaluationObjectMapper.selectCount(objectQueryWrapper);
        //转办人数
        LambdaQueryWrapper<DhrEaEvaluationTransfer> transferLambdaQueryWrapper = new LambdaQueryWrapper<>();
        transferLambdaQueryWrapper.eq(DhrEaEvaluationTransfer::getEaId, id);
        List<DhrEaEvaluationTransfer> evaluationTransferList = dhrEaEvaluationTransferMapper.selectList(transferLambdaQueryWrapper);
        long evaluationTransferNumber = evaluationTransferList.size();
        //组装数据
        DhrEaEvaluationEmpCountResponse response = new DhrEaEvaluationEmpCountResponse();
        response.setEvaluatorReviewerNumber(evaluationReviewerNumber);
        response.setEvaluationObjectNumber(Long.valueOf(evaluationObjectNumber));
        response.setEvaluationTransferorNumber(evaluationTransferNumber);
        return response;
    }

    @Override
    public Boolean eaBpmCheck(String businessNo, String processInstanceId, DhrEaActivityBpmCheckRequest request) {
        String end = "end";
        String[] split = businessNo.split(BUSINESS_NO_DELIMITER);
        // 检查是否允许提交流程：停用禁止提交
        DhrEaActivityInfo eaActivityInfo = getById(split[0]);
        if (DhrEaConstant.ActivityInfo.EA_STATUS_TY.equals(eaActivityInfo.getEaStatus())) {
            throw new CommRunException("dhr.talent.error.activityDisabledNotAllowedSubmitted");
        }
        // 校验通过,记录当前审批阶段
        String currentStage;
        String taskKey = request.getTaskKey();
        String id = request.getId();
        if (end.equals(request.getType())) {
            currentStage = DhrEaConstant.ApprovalCurrentStage.FINISH;
        } else {
            currentStage = taskKey.replaceAll(id, "").replaceAll(":", "");
        }
        return dhrEaEvaluationObjectService.updateCurrentStage(processInstanceId, currentStage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateTeamReport(DhrEaObjectTeamReportRequest request) {
        // 保存团队报告名称、成员
        Long teamHistoryId = dhrEaReportTeamHistoryService.saveTeamHistory(request.getEaId(), request.getReportName(), request.getEmpCodes());
        return generateTeamReport(request, teamHistoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateTeamReport(DhrEaObjectTeamReportRequest request, Long teamHistoryId) {
        Long eaId = request.getEaId();
        String reportName = request.getReportName();
        List<String> empCodes = request.getEmpCodes();
        // 过滤测评对象状态为已关闭文件
        empCodes = filterQuestionnaireStatus(eaId, empCodes);
        request.setEmpCodes(empCodes);
        // 检查该活动是否可以生成团队报告
        generateTeamReportCheck(request);
        DhrEaObjectTeamReportResponse teamReportResponse = new DhrEaObjectTeamReportResponse();
        // 0.公用数据准备
        // 获取测评活动相关能力项
        List<DhrEaReportAbilityResponse> eaAbilityList = dhrEaReportAbilityScoreService.findEaAbilityDistinctList(eaId);
        // 获取测评活动启用的测评者类型
        List<DhrEaEvaluationWeightResponse> weightResponseList = dhrEaEvaluationWeightService.findEnabledListByEaId(eaId);
        List<String> evaluationTypeList = weightResponseList.stream().map(DhrEaEvaluationWeightResponse::getEvaluationType).collect(Collectors.toList());
        // 获取测评活动能力项相关分数
        List<DhrEaReportAbilityScoreResponse> abilityScoreResponseList = dhrEaReportAbilityScoreService.findByEmpCodeAndEaId(empCodes, request.getEaId());
        Map<String, List<DhrEaReportAbilityScoreResponse>> abilityScoreMap = abilityScoreResponseList.stream().collect(Collectors.groupingBy(DhrEaReportAbilityScoreResponse::getAbilityName));
        // 计算测评活动能力项团队均分
        List<DhrEaObjectTeamReportResponse.TeamAbilityScore> teamAbilityScoreList = countTeamAbilityScoreList(abilityScoreMap);

        // 1.报告第一部分-封面
        teamReportResponse.setReportName(reportName);
        teamReportResponse.setReportTime(DateUtil.today());

        // 2.报告第二部分-前言
        DhrEaObjectTeamReportResponse.Preface preface = countTeamPreface(request, eaAbilityList, weightResponseList);
        teamReportResponse.setPreface(preface);

        // 3.报告第三部分-指标定义
        teamReportResponse.setIndicatorDefinitionList(eaAbilityList);
        // 5.报告第五部分-概况
        DhrEaObjectTeamReportResponse.OverviewInfo overviewInfo = countTeamOverviewInfo(request, teamAbilityScoreList, evaluationTypeList);
        teamReportResponse.setOverviewInfo(overviewInfo);

        // 6.报告第六部分-团队优势与待发展
        List<DhrEaObjectTeamReportResponse.TeamAbilityScore> advantageAbilityList = teamAbilityScoreList.stream().filter(abilityScore -> BigDecimal.valueOf(4).compareTo(abilityScore.getComprehensiveScore()) < 0)
                .sorted(Comparator.comparing(DhrEaObjectTeamReportResponse.TeamAbilityScore::getComprehensiveScore).reversed()).limit(3).collect(Collectors.toList());
        List<DhrEaObjectTeamReportResponse.TeamAbilityScore> developAbilityList = teamAbilityScoreList.stream().filter(abilityScore -> BigDecimal.valueOf(3).compareTo(abilityScore.getComprehensiveScore()) > 0)
                .sorted(Comparator.comparing(DhrEaObjectTeamReportResponse.TeamAbilityScore::getComprehensiveScore)).limit(3).collect(Collectors.toList());
        teamReportResponse.setAdvantageAbilityList(advantageAbilityList);
        teamReportResponse.setDevelopAbilityList(developAbilityList);

        // 7.报告第七部分-团队自我认知偏差
        List<DhrEaObjectTeamReportResponse.WeightHeader> cognitiveBiasHeaderList = handelCognitiveBiasHeaderList(weightResponseList);
        teamReportResponse.setCognitiveBiasHeaderList(cognitiveBiasHeaderList);
        teamReportResponse.setCognitiveBiasList(teamAbilityScoreList);

        // 8.报告第八部分-团队发展建议
        DhrEaObjectTeamReportResponse.DevelopmentAdvice developmentAdvice = countTeamDevelopmentAdvice(teamAbilityScoreList);
        teamReportResponse.setDevelopmentAdvice(developmentAdvice);

        // 9.报告第九部分-人员详情
        List<DhrEaObjectTeamReportResponse.WeightHeader> teamMemberHeaderList = handelTeamMemberHeaderList(weightResponseList);
        teamReportResponse.setTeamMemberHeaderList(teamMemberHeaderList);
        teamReportResponse.setTeamMemberDetailList(abilityScoreResponseList);
        // 转换团队报告pdf
        convertTeamPdf(teamReportResponse, request, teamHistoryId);
        return true;
    }

    /**
     * 处理团队自我认知偏差动态表头
     *
     * @param weightResponseList
     * @return
     */
    private List<DhrEaObjectTeamReportResponse.WeightHeader> handelCognitiveBiasHeaderList(List<DhrEaEvaluationWeightResponse> weightResponseList) {
        List<DhrEaObjectTeamReportResponse.WeightHeader> list = new ArrayList<>();
        for (DhrEaEvaluationWeightResponse weightResponse : weightResponseList) {
            DhrEaObjectTeamReportResponse.WeightHeader weightHeader = new DhrEaObjectTeamReportResponse.WeightHeader();
            if (DhrEaConstant.EvaluationType.SJ_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("superiorScore");
                weightHeader.setName("上级");
            }
            if (DhrEaConstant.EvaluationType.XJ_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("lowerLevelScore");
                weightHeader.setName("下级");
            }
            if (DhrEaConstant.EvaluationType.PJ_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("sameLevelScore");
                weightHeader.setName("平级");
            }
            if (DhrEaConstant.EvaluationType.QT_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("otherScore");
                weightHeader.setName("其他");
            }
            if (DhrEaConstant.EvaluationType.BR_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("myselfScore");
                weightHeader.setName("自评得分");
            }
            list.add(weightHeader);
        }
        setWeightHeader(list, "comprehensiveScore", "综合得分");
        return list;
    }

    /**
     * 处理团队成员动态表头
     *
     * @param weightResponseList
     * @return
     */
    private List<DhrEaObjectTeamReportResponse.WeightHeader> handelTeamMemberHeaderList(List<DhrEaEvaluationWeightResponse> weightResponseList) {
        List<DhrEaObjectTeamReportResponse.WeightHeader> list = new ArrayList<>();
        for (DhrEaEvaluationWeightResponse weightResponse : weightResponseList) {
            DhrEaObjectTeamReportResponse.WeightHeader weightHeader = new DhrEaObjectTeamReportResponse.WeightHeader();
            if (DhrEaConstant.EvaluationType.SJ_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("superiorScore");
                weightHeader.setName("上级");
            }
            if (DhrEaConstant.EvaluationType.XJ_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("lowerLevelScore");
                weightHeader.setName("下级");
            }
            if (DhrEaConstant.EvaluationType.PJ_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("sameLevelScore");
                weightHeader.setName("同事");
            }
            if (DhrEaConstant.EvaluationType.QT_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("otherScore");
                weightHeader.setName("其他");
            }
            if (DhrEaConstant.EvaluationType.BR_TYPE.equals(weightResponse.getEvaluationType())) {
                weightHeader.setKey("myselfScore");
                weightHeader.setName("自评得分");
            }
            list.add(weightHeader);
        }
        setWeightHeader(list, "comprehensiveScore", "综合得分");
        return list;
    }

    /**
     * 过滤测评对象问卷状态
     *
     * @param eaId
     * @param empCodes
     * @return
     */
    private List<String> filterQuestionnaireStatus(Long eaId, List<String> empCodes) {
        if (CollUtil.isNotEmpty(empCodes)) {
            LambdaQueryWrapper<DhrEaEvaluationObject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DhrEaEvaluationObject::getEmpCode, empCodes)
                    .and(i -> i.ne(DhrEaEvaluationObject::getQuestionnaireStatus, DhrEaConstant.ObjectQuestionnaireStatus.CLOSE));
            queryWrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
            List<DhrEaEvaluationObject> eaEvaluationObjects = dhrEaEvaluationObjectMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(eaEvaluationObjects)) {
                List<String> empCodesFilter = eaEvaluationObjects.stream().map(DhrEaEvaluationObject::getEmpCode).collect(Collectors.toList());
                return empCodesFilter;
            } else {
                return new ArrayList<>();
            }
        }
        return empCodes;
    }

    /**
     * 转换团队报告pdf
     *
     * @param teamReportResponse
     * @param teamHistoryId
     * @return
     */
    private Boolean convertTeamPdf(DhrEaObjectTeamReportResponse teamReportResponse, DhrEaObjectTeamReportRequest request, Long teamHistoryId) {
        DhrEaActivityInfo eaActivityInfo = this.getById(request.getEaId());
        // 填充测评活动名称和编码
        teamReportResponse.setEaName(eaActivityInfo.getEaName());
        teamReportResponse.setEaCode(eaActivityInfo.getEaCode());
        // 生成报表
        String reportFileName = eaActivityInfo.getEaName() + "-" + request.getReportName() + "-" + DateUtil.today();
        FileResponseDto pdfData = this.generateReportFile("teamReportData", JSON.toJSONString(teamReportResponse), "teamReport.ftl", reportFileName);
        // 更新团队报告历史记录中文件ID、文件名称
        dhrEaReportTeamHistoryService.updateTeamHistoryFile(teamHistoryId, pdfData.getId(), pdfData.getName());
        return true;
    }

    @Override
    public Boolean deleteTeamReport(Long id) {
        return dhrEaReportTeamHistoryService.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reGenerateTeamReport(Long id) {
        // 获取当前报告生成参数
        DhrEaReportTeamHistoryResponse teamHistoryResponse = dhrEaReportTeamHistoryService.getById(id);
        DhrEaObjectTeamReportRequest request = new DhrEaObjectTeamReportRequest();
        request.setEaId(teamHistoryResponse.getEaId());
        request.setReportName(teamHistoryResponse.getReportName());
        List<DhrEaReportTeamMemberResponse> teamMemberResponseList = teamHistoryResponse.getTeamMemberResponseList();
        List<String> empCodeList = teamMemberResponseList.stream().map(DhrEaReportTeamMemberResponse::getEmpCode).collect(Collectors.toList());
        request.setEmpCodes(empCodeList);
        return this.generateTeamReport(request, id);
    }


    @Override
    public boolean generatePersonalReport(DhrEaObjectGenerateReportRequest request) {
        //检查是否允许生成报告
        this.checkEaActivityInfo(request.getEaId(), DhrEaConstant.ReportType.PERSONAL_REPORT);
        //查询各类型权重比例组合的文本
        List<DhrEaEvaluationWeight> weightList = dhrEaEvaluationWeightMapper.selectByEaId(request.getEaId()).stream().filter(a -> DhrEaConstant.ENABLE.equals(a.getEnabled()))
                .collect(Collectors.toList());
        Map<String, String> evaluationTypeMap = talentFeignMethodUtil.findByDicGroupCode(TalentEnumCodeConstant.EVALUATION_TYPE);
        List<String> weights = weightList.stream().map(weight -> weight.getWeight() + "%" + evaluationTypeMap.get(weight.getEvaluationType()))
                .collect(Collectors.toList());
        String evaluationWeightText = StrUtils.join(weights, " + ");
        //组装启用的类型和对应的分数属性字段名 例如 key：superiorScore value：上级
        Map<String, String> enableEvaluationTypeMap = this.getEnableEvaluationTypeMap(weightList, evaluationTypeMap);
        //查询测评活动
        DhrEaActivityInfo dhrEaActivityInfo = superMapper.selectById(request.getEaId());
        //查询出符合生成报告的测评对象
        List<DhrEaEvaluationObject> objectList = dhrEaEvaluationObjectMapper.getAllowedGenerateReportEmpList(request);
        if (CollectionUtils.isEmpty(objectList)) {
            throw new CommRunException("dhr.talent.error.activity.report.personal.nonScore");
        }
        objectList = objectList.stream().limit(20).collect(Collectors.toList());
        List<String> empCodes = objectList.stream().map(DhrEaEvaluationObject::getEmpCode).collect(Collectors.toList());
        //查询能力项分数统计数据
        List<DhrEaReportAbilityScoreResponse> abilityScoreResponseList = dhrEaReportAbilityScoreService.findByEmpCodeAndEaId(empCodes, request.getEaId());
        Map<String, List<DhrEaReportAbilityScoreResponse>> abilityScoreMap = abilityScoreResponseList.stream().collect(Collectors.groupingBy(DhrEaReportAbilityScoreResponse::getEmpCode));
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        SecurityContext context = SecurityContextHolder.getContext();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        List<DhrEaEvaluationObject> finalObjectList = objectList;
        cachedThreadPool.execute(() -> {
            RequestHeaderHandler.setHeaderMap(headerMap);
            // 保证feign调用正常
            SecurityContextHolder.setContext(context);
            RequestContextHolder.setRequestAttributes(requestAttributes);
            finalObjectList.forEach(dhrEaEvaluationObject -> {
                DhrEaObjectGenerateReportResponse response = new DhrEaObjectGenerateReportResponse();
                //组装个人报告数据
                response.setEnableEvaluationTypeMap(enableEvaluationTypeMap);
                response.setEvaluationWeightText(evaluationWeightText);
                response.setEaName(dhrEaActivityInfo.getEaName());
                response.setEaCode(dhrEaActivityInfo.getEaCode());
                response.setEvaluationObjScopeComments(dhrEaActivityInfo.getEvaluationObjScopeComments());
                String personalReportData = this.buildPersonalReportData(dhrEaEvaluationObject, abilityScoreMap, response);
                // 生成报表
                String reportFileName = dhrEaActivityInfo.getEaName() + "-" + dhrEaEvaluationObject.getEmpCode() + "-" + dhrEaEvaluationObject.getEmpName() + "-" + dhrEaEvaluationObject.getEmpPositionName();
                FileResponseDto fileResponseDto = this.generateReportFile("personalReportData", personalReportData, "personalReport.ftl", reportFileName);
                //报告生成后保存生成数据
                DhrEaReportPersonalHistory dhrEaReportPersonalHistory = BeanUtil.copyProperties(dhrEaEvaluationObject, DhrEaReportPersonalHistory.class);
                dhrEaReportPersonalHistory.setReportName(fileResponseDto.getName());
                dhrEaReportPersonalHistory.setFileId(fileResponseDto.getId());
                dhrEaReportPersonalHistoryService.savePersonalReportHistory(dhrEaReportPersonalHistory);
            });
        });
        return true;
    }

    @Override
    public ResponseVO batchExportReport(DhrEaBatchExportReportRequest request, HttpServletResponse response) {
        DhrEaActivityInfo dhrEaActivityInfo = superMapper.selectById(request.getEaId());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String zipFileName = dhrEaActivityInfo.getEaName() + "-" + DhrEaConstant.ReportType.MAP.get(request.getType()) + simpleDateFormat.format(new Date());
        List<Long> fileIds = request.getFileIds();
        if (CollectionUtils.isEmpty(fileIds)) {
            //个人报告
            if (DhrEaConstant.ReportType.PERSONAL_REPORT.equals(request.getType())) {
                List<DhrEaPersonalReportListResponse> list = dhrEaEvaluationObjectMapper.personalReportEmpList(new Page<>(-1, -1),
                        BeanUtil.copyProperties(request, DhrEaPersonalReportListRequest.class));
                fileIds = list.stream().filter(listResponse -> CharSequenceUtil.isNotBlank(listResponse.getFileId())).distinct().map(listResponse ->
                        Long.valueOf(listResponse.getFileId())).collect(Collectors.toList());
            }
            // 团队报告
            if (DhrEaConstant.ReportType.TEAM_REPORT.equals(request.getType())) {
                List<DhrEaReportTeamHistoryResponse> teamHistoryResponseList = dhrEaReportTeamHistoryService.teamReportAllList(request.getEaId());
                fileIds = teamHistoryResponseList.stream().filter(listResponse -> CharSequenceUtil.isNotBlank(listResponse.getFileId()))
                        .map(listResponse -> Long.valueOf(listResponse.getFileId())).collect(Collectors.toList());
            }
        }
        if (fileIds.size() > 20) {
            throw new CommRunException("dhr.talent.import.evaluation.object.error.export");
        }
        Map<String, byte[]> map = talentFeignMethodUtil.batchDownload(fileIds);
        try {
            //获取文件字节数组
            InputStream[] fileStreams = new InputStream[map.size()];
            String[] fileNames = new String[map.size()];
            int i = 0;
            for (Map.Entry<String, byte[]> entry : map.entrySet()) {
                fileNames[i] = entry.getKey();
                fileStreams[i] = new ByteArrayInputStream(entry.getValue());
                i++;
            }
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName + ".zip", "UTF-8"));
            //多个文件压缩成压缩包返回
            ZipUtil.zip(response.getOutputStream(), fileNames, fileStreams);
        } catch (Exception e) {
            log.error("调用minio下载文件, 错误信息为：" + e.getMessage());
        }
        return new ResponseVO<>();
    }

    @Override
    public boolean sendPersonalReportEmail(DhrEaPersonalSendReportEmailRequest request) {
        //查询需要发送邮件的测评对象
        List<DhrEaPersonalReportListResponse> list = dhrEaEvaluationObjectMapper.personalReportEmpList(new Page<>(-1, -1),
                BeanUtil.copyProperties(request, DhrEaPersonalReportListRequest.class));
        Map<String, String> map = list.stream().filter(response -> CharSequenceUtil.isNotBlank(response.getFileId()))
                .collect(Collectors.toMap(DhrEaPersonalReportListResponse::getEmpCode, DhrEaPersonalReportListResponse::getFileId));
        if (MapUtils.isEmpty(map)) {
            throw new CommRunException("dhr.talent.error.activity.report.personal.nonObject");
        }
        List<String> empCodes = new ArrayList<>(map.keySet());
        Map<String, EmployeeBasicInfoResp> empMap = talentFeignMethodUtil.findByEmpCodes(empCodes);
        map.forEach((empCode, fileId) -> {
            EmployeeBasicInfoResp basicInfoResp = empMap.get(empCode);
            if (basicInfoResp == null || CharSequenceUtil.isEmpty(basicInfoResp.getEmail())) {
                return;
            }
            //异步发送邮件
            Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
            SecurityContext context = SecurityContextHolder.getContext();
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            cachedThreadPool.execute(() -> {
                RequestHeaderHandler.setHeaderMap(headerMap);
                // 保证feign调用正常
                SecurityContextHolder.setContext(context);
                RequestContextHolder.setRequestAttributes(requestAttributes);
                String url = downloadFileUrl + "/utility/file/dfs/download?fileId=" + fileId;
                String content = request.getContent() + "\n报告下载地址为:" + url;
                EmailParamDto emailParamDto = new EmailParamDto(basicInfoResp.getEmail(), null, request.getSubject(), content, null, null);
                talentFeignMethodUtil.sendEmail(emailParamDto);
            });
        });
        return true;
    }

    @Override
    public boolean sendTeamReportEmail(DhrEaTeamSendReportEmailRequest request) {
        DhrEaReportTeamHistoryResponse teamHistoryResponse = dhrEaReportTeamHistoryService.getById(request.getTeamReportId());
        String fileId = teamHistoryResponse.getFileId();
        if (CharSequenceUtil.isNotBlank(fileId)) {
            //发送邮件
            String url = downloadFileUrl + "/utility/file/dfs/download?fileId=" + fileId;
            String content = request.getContent() + "\n报告下载地址为:" + url;
            EmailParamDto emailParamDto = new EmailParamDto(request.getEmailReceives(), null, request.getSubject(), content, null, null);
            return talentFeignMethodUtil.sendEmail(emailParamDto);
        }
        return false;
    }

    @Override
    public void updateEvaluatorNumByLock(Long eaId, Integer finish, Integer unFinish, Integer count) {
        if (finish == null && unFinish == null && count == null) {
            return;
        }
        LambdaUpdateWrapper<DhrEaActivityInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DhrEaActivityInfo::getId, eaId);
        DhrEaActivityInfo activityInfo = getOne(wrapper);
        if (activityInfo == null) {
            return;
        }
        Integer finalFinish = activityInfo.getEvaluationFinishedCount();
        Integer finalCount = activityInfo.getEvaluationCount();
        if (finish != null) {
            finalFinish = Math.addExact(activityInfo.getEvaluationFinishedCount(), finish);
            wrapper.eq(DhrEaActivityInfo::getEvaluationFinishedCount, activityInfo.getEvaluationFinishedCount());
            wrapper.set(DhrEaActivityInfo::getEvaluationFinishedCount, finalFinish);
        }
        if (unFinish != null) {
            wrapper.eq(DhrEaActivityInfo::getEvaluationUnfinishedCount, activityInfo.getEvaluationUnfinishedCount());
            wrapper.set(DhrEaActivityInfo::getEvaluationUnfinishedCount, Math.addExact(activityInfo.getEvaluationUnfinishedCount(), unFinish));
        }
        if (count != null) {
            finalCount = Math.addExact(activityInfo.getEvaluationCount(), count);
            wrapper.eq(DhrEaActivityInfo::getEvaluationCount, activityInfo.getEvaluationCount());
            wrapper.set(DhrEaActivityInfo::getEvaluationCount, finalCount);
        }
        BigDecimal finishRate = finalCount == 0 ? BigDecimal.valueOf(0) : BigDecimal.valueOf(finalFinish.doubleValue() / finalCount);
        wrapper.set(DhrEaActivityInfo::getEvaluationRate, finishRate.setScale(2, RoundingMode.HALF_UP));
        if (update(wrapper)) {
            return;
        }
        updateEvaluatorNumByLock(eaId, finish, unFinish, count);
    }


    /**
     * 计算测评活动能力项团队均分
     *
     * @param abilityScoreMap
     * @return
     */
    private List<DhrEaObjectTeamReportResponse.TeamAbilityScore> countTeamAbilityScoreList(Map<String, List<DhrEaReportAbilityScoreResponse>> abilityScoreMap) {
        List<DhrEaObjectTeamReportResponse.TeamAbilityScore> teamAbilityScoreList = new ArrayList<>();

        for (Map.Entry<String, List<DhrEaReportAbilityScoreResponse>> entry : abilityScoreMap.entrySet()) {
            String abilityName = entry.getKey();
            DhrEaObjectTeamReportResponse.TeamAbilityScore abilityScoreResponse = new DhrEaObjectTeamReportResponse.TeamAbilityScore();
            abilityScoreResponse.setAbilityName(abilityName);
            List<DhrEaReportAbilityScoreResponse> abilityScoreResponses = entry.getValue();
            abilityScoreResponses.forEach(abilityScore -> {
                if (abilityScoreResponse.getAbilityCode() == null) {
                    abilityScoreResponse.setAbilityCode(abilityScore.getAbilityCode());
                }
                // 设置能力项定义
                abilityScoreResponse.setAbilityDefinition(abilityScore.getAbilityDefinition());
                abilityScoreResponse.setDimenId(abilityScore.getDimenId());
                // 计算总分
                abilityScoreResponse.setComprehensiveScore(addBigDecimal(abilityScoreResponse.getComprehensiveScore(), abilityScore.getComprehensiveScore()));
                abilityScoreResponse.setMyselfScore(addBigDecimal(abilityScoreResponse.getMyselfScore(), abilityScore.getMyselfScore()));
                abilityScoreResponse.setOtherScore(addBigDecimal(abilityScoreResponse.getOtherScore(), abilityScore.getOtherScore()));
                abilityScoreResponse.setSameLevelScore(addBigDecimal(abilityScoreResponse.getSameLevelScore(), abilityScore.getSameLevelScore()));
                abilityScoreResponse.setSuperiorScore(addBigDecimal(abilityScoreResponse.getSuperiorScore(), abilityScore.getSuperiorScore()));
                abilityScoreResponse.setLowerLevelScore(addBigDecimal(abilityScoreResponse.getLowerLevelScore(), abilityScore.getLowerLevelScore()));
                // 计算最大、最小值
                BigDecimal comprehensiveScoreMin = abilityScoreResponse.getComprehensiveScoreMin();
                BigDecimal comprehensiveScoreMax = abilityScoreResponse.getComprehensiveScoreMax();
                // 最大最小值为null时，设置初始比较值
                if (comprehensiveScoreMin == null) {
                    abilityScoreResponse.setComprehensiveScoreMin(abilityScore.getComprehensiveScore());
                    comprehensiveScoreMin = abilityScore.getComprehensiveScore();
                }
                if (comprehensiveScoreMax == null) {
                    abilityScoreResponse.setComprehensiveScoreMax(abilityScore.getComprehensiveScore());
                    comprehensiveScoreMax = abilityScore.getComprehensiveScore();
                }
                if (comprehensiveScoreMin.compareTo(abilityScore.getComprehensiveScore()) > 0) {
                    abilityScoreResponse.setComprehensiveScoreMin(abilityScore.getComprehensiveScore());
                } else if (comprehensiveScoreMax.compareTo(abilityScore.getComprehensiveScore()) < 0) {
                    abilityScoreResponse.setComprehensiveScoreMax(abilityScore.getComprehensiveScore());
                }
            });
            // 计算平均值
            BigDecimal size = BigDecimal.valueOf(abilityScoreResponses.size());
            abilityScoreResponse.setComprehensiveScore(abilityScoreResponse.getComprehensiveScore()
                    .divide(size, TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            abilityScoreResponse.setMyselfScore(abilityScoreResponse.getMyselfScore()
                    .divide(size, TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            abilityScoreResponse.setOtherScore(abilityScoreResponse.getOtherScore()
                    .divide(size, TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            abilityScoreResponse.setSameLevelScore(abilityScoreResponse.getSameLevelScore()
                    .divide(size, TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            abilityScoreResponse.setSuperiorScore(abilityScoreResponse.getSuperiorScore()
                    .divide(size, TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            abilityScoreResponse.setLowerLevelScore(abilityScoreResponse.getLowerLevelScore()
                    .divide(size, TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            teamAbilityScoreList.add(abilityScoreResponse);
        }
        // 根据能力项编码排序
        if (CollUtil.isNotEmpty(teamAbilityScoreList)) {
            teamAbilityScoreList = teamAbilityScoreList.stream().sorted(Comparator.comparing(DhrEaObjectTeamReportResponse.TeamAbilityScore::getAbilityCode)).collect(Collectors.toList());
        }
        return teamAbilityScoreList;
    }

    /**
     * 团队报告分数计算相加
     *
     * @param obj1
     * @param obj2
     * @return
     */
    private BigDecimal addBigDecimal(BigDecimal obj1, BigDecimal obj2) {
        return obj1.add(obj2 == null ? BigDecimal.valueOf(0) : obj2);
    }

    /**
     * 计算团队报告-前言
     *
     * @param request
     * @param eaAbilityList
     * @param weightResponseList
     * @return
     */
    private DhrEaObjectTeamReportResponse.Preface countTeamPreface(DhrEaObjectTeamReportRequest request,
                                                                   List<DhrEaReportAbilityResponse> eaAbilityList,
                                                                   List<DhrEaEvaluationWeightResponse> weightResponseList) {
        Long eaId = request.getEaId();
        String reportName = request.getReportName();
        List<String> empCodes = request.getEmpCodes();
        DhrEaObjectTeamReportResponse.Preface preface = new DhrEaObjectTeamReportResponse.Preface();
        preface.setReportName(reportName);
        preface.setObjectEmpSize(empCodes.size());
        preface.setWeightResponseList(weightResponseList);
        preface.setAbilityResponsesList(eaAbilityList);
        // 获取评价者类别数据图表：有效数据、邀请、完成率
        List<DhrEaObjectTeamReportResponse.Preface.EvaluationTypeChart> evaluationTypeChartList = new ArrayList<>();
        Map<String, Integer> evaluationTypeValidData = dhrEaObjReviewerRelService
                .getEvaluationTypeStatisticsByStatus(eaId, DhrEaConstant.AnswerStatus.AN_STATUS_YDT, empCodes);
        Map<String, Integer> evaluationTypeInvitationData = dhrEaObjReviewerRelService
                .getEvaluationTypeStatisticsByIsSend(eaId, true, empCodes);
        for (DhrEaEvaluationWeightResponse weightResponse : weightResponseList) {
            DhrEaObjectTeamReportResponse.Preface.EvaluationTypeChart evaluationTypeChart = new DhrEaObjectTeamReportResponse.Preface.EvaluationTypeChart();
            evaluationTypeChart.setEvaluationTypeName(weightResponse.getEvaluationTypeName());
            Integer validDataSize = evaluationTypeValidData.getOrDefault(weightResponse.getEvaluationType(), 0);
            Integer invitationDataSize = evaluationTypeInvitationData.get(weightResponse.getEvaluationType());
            if (invitationDataSize != null && !invitationDataSize.equals(0)) {
                evaluationTypeChart.setValidDataSize(validDataSize);
                evaluationTypeChart.setInvitationDataSize(invitationDataSize);
                evaluationTypeChart.setCompletionRate(BigDecimal.valueOf(validDataSize).divide(BigDecimal.valueOf(invitationDataSize), 2, RoundingMode.HALF_UP));
            } else {
                evaluationTypeChart.setValidDataSize(0);
                evaluationTypeChart.setInvitationDataSize(0);
                evaluationTypeChart.setCompletionRate(BigDecimal.valueOf(0));
            }
            evaluationTypeChartList.add(evaluationTypeChart);
        }
        preface.setEvaluationTypeChartList(evaluationTypeChartList);
        return preface;
    }

    /**
     * 计算团队概况数据
     *
     * @param request
     * @param teamAbilityScoreList
     * @param evaluationTypeList
     * @return
     */
    private DhrEaObjectTeamReportResponse.OverviewInfo countTeamOverviewInfo(DhrEaObjectTeamReportRequest request,
                                                                             List<DhrEaObjectTeamReportResponse.TeamAbilityScore> teamAbilityScoreList,
                                                                             List<String> evaluationTypeList) {
        Long eaId = request.getEaId();
        List<String> empCodes = request.getEmpCodes();
        List<DhrEaReportTotalScore> totalScoreList = dhrEaReportTotalScoreService.getListByEmpCode(empCodes, eaId);
        List<DhrEaObjectTeamReportResponse.WeightHeader> weightHeaderList = new ArrayList<>();
        DhrEaObjectTeamReportResponse.OverviewInfo overviewInfo = new DhrEaObjectTeamReportResponse.OverviewInfo();
        overviewInfo.setAbilityScoreList(teamAbilityScoreList);
        OptionalDouble comprehensiveScoreOptional = totalScoreList.stream().filter(r -> r.getLowerLevelScore() != null).mapToDouble(totalScore -> totalScore.getComprehensiveScore()
                .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP).doubleValue()).average();
        double comprehensiveScore = comprehensiveScoreOptional.isPresent() ? comprehensiveScoreOptional.getAsDouble() : 0L;
        overviewInfo.setComprehensiveTotalScore(BigDecimal.valueOf(comprehensiveScore).setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
        setWeightHeader(weightHeaderList, "comprehensiveTotalScore", "综合总均分");
        if (evaluationTypeList.contains(DhrEaConstant.EvaluationType.SJ_TYPE)) {
            setWeightHeader(weightHeaderList, "superiorTotalScore", "上级总均分");
            List<DhrEaReportTotalScore> tempList = totalScoreList.stream().filter(r -> r.getSuperiorScore() != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tempList)) {
                OptionalDouble superiorScoreOptional = tempList.stream().mapToDouble(totalScore -> totalScore.getSuperiorScore()
                        .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP).doubleValue()).average();
                double superiorScore = superiorScoreOptional.isPresent() ? superiorScoreOptional.getAsDouble() : 0L;
                overviewInfo.setSuperiorTotalScore(BigDecimal.valueOf(superiorScore).setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            }
        }
        if (evaluationTypeList.contains(DhrEaConstant.EvaluationType.XJ_TYPE)) {
            setWeightHeader(weightHeaderList, "lowerLevelTotalScore", "下级总均分");
            List<DhrEaReportTotalScore> tempList = totalScoreList.stream().filter(r -> r.getLowerLevelScore() != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tempList)) {
                OptionalDouble lowerLevelScoreOptional = tempList.stream().mapToDouble(totalScore -> totalScore.getLowerLevelScore()
                        .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP).doubleValue()).average();
                double lowerLevelScore = lowerLevelScoreOptional.isPresent() ? lowerLevelScoreOptional.getAsDouble() : 0L;
                overviewInfo.setLowerLevelTotalScore(BigDecimal.valueOf(lowerLevelScore).setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            }
        }
        if (evaluationTypeList.contains(DhrEaConstant.EvaluationType.PJ_TYPE)) {
            setWeightHeader(weightHeaderList, "sameLevelTotalScore", "平级总均分");
            List<DhrEaReportTotalScore> tempList = totalScoreList.stream().filter(r -> r.getSameLevelScore() != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tempList)) {
                OptionalDouble sameLevelScoreOptional = tempList.stream().mapToDouble(totalScore -> totalScore.getSameLevelScore()
                        .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP).doubleValue()).average();
                double sameLevelScore = sameLevelScoreOptional.isPresent() ? sameLevelScoreOptional.getAsDouble() : 0L;
                overviewInfo.setSameLevelTotalScore(BigDecimal.valueOf(sameLevelScore).setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            }
        }
        if (evaluationTypeList.contains(DhrEaConstant.EvaluationType.QT_TYPE)) {
            setWeightHeader(weightHeaderList, "otherTotalScore", "其他总均分");
            List<DhrEaReportTotalScore> tempList = totalScoreList.stream().filter(r -> r.getOtherScore() != null).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tempList)) {
                OptionalDouble otherScoreOptional = tempList.stream().mapToDouble(totalScore -> totalScore.getOtherScore()
                        .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP).doubleValue()).average();
                double otherScore = otherScoreOptional.isPresent() ? otherScoreOptional.getAsDouble() : 0L;
                overviewInfo.setOtherTotalScore(BigDecimal.valueOf(otherScore).setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
            }
        }
        setWeightHeader(weightHeaderList, "myselfTotalScore", "本人总均分");
        List<DhrEaReportTotalScore> tempList = totalScoreList.stream().filter(r -> r.getMyselfScore() != null).collect(Collectors.toList());
        OptionalDouble myselfScoreOptional = tempList.stream().mapToDouble(totalScore -> totalScore.getMyselfScore()
                .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP).doubleValue()).average();
        double myselfScore = myselfScoreOptional.isPresent() ? myselfScoreOptional.getAsDouble() : 0L;
        overviewInfo.setMyselfTotalScore(BigDecimal.valueOf(myselfScore).setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP));
        overviewInfo.setWeightHeader(weightHeaderList);
        return overviewInfo;
    }

    /**
     * 设置测评关系类别 动态表头
     *
     * @param list
     * @param key
     * @param name
     */
    private void setWeightHeader(List<DhrEaObjectTeamReportResponse.WeightHeader> list, String key, String name) {
        DhrEaObjectTeamReportResponse.WeightHeader header = new DhrEaObjectTeamReportResponse.WeightHeader();
        header.setKey(key);
        header.setName(name);
        list.add(header);
    }

    /**
     * 计算团队发展建议数据
     *
     * @param teamAbilityScoreList
     * @return
     */
    private DhrEaObjectTeamReportResponse.DevelopmentAdvice countTeamDevelopmentAdvice(List<DhrEaObjectTeamReportResponse.TeamAbilityScore> teamAbilityScoreList) {
        DhrEaObjectTeamReportResponse.DevelopmentAdvice developmentAdvice = new DhrEaObjectTeamReportResponse.DevelopmentAdvice();
        // 计算所有能力项综合得分、离散度的平均值
        BigDecimal comprehensiveScoreAllAvg = BigDecimal.valueOf(teamAbilityScoreList.stream()
                .mapToDouble(abilityScore -> abilityScore.getComprehensiveScore().doubleValue()).average().getAsDouble())
                .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP);
        developmentAdvice.setComprehensiveScoreAllAvg(comprehensiveScoreAllAvg);
        List<DhrEaObjectTeamReportResponse.DevelopmentAdvice.AbilityScoreDispersion> abilityScoreDispersionList = new ArrayList<>();
        for (DhrEaObjectTeamReportResponse.TeamAbilityScore teamAbilityScore : teamAbilityScoreList) {
            DhrEaObjectTeamReportResponse.DevelopmentAdvice.AbilityScoreDispersion abilityScoreDispersion = new DhrEaObjectTeamReportResponse.DevelopmentAdvice.AbilityScoreDispersion();
            abilityScoreDispersion.setAbilityName(teamAbilityScore.getAbilityName());
            abilityScoreDispersion.setComprehensiveScore(teamAbilityScore.getComprehensiveScore());
            // 计算离散度：（综合得分-综合得分均分）取绝对值
            BigDecimal comprehensiveDispersion = teamAbilityScore.getComprehensiveScore().subtract(comprehensiveScoreAllAvg).abs();
            abilityScoreDispersion.setComprehensiveDispersion(comprehensiveDispersion);
            abilityScoreDispersionList.add(abilityScoreDispersion);
        }
        BigDecimal comprehensiveDispersionAllAvg = BigDecimal.valueOf(abilityScoreDispersionList.stream()
                .mapToDouble(abilityScoreDispersion -> abilityScoreDispersion.getComprehensiveDispersion().doubleValue()).average().getAsDouble())
                .setScale(TEAM_REPORT_SCALE, RoundingMode.HALF_UP);
        developmentAdvice.setComprehensiveDispersionAllAvg(comprehensiveDispersionAllAvg);
        // 计算能力项所属区域
        for (DhrEaObjectTeamReportResponse.DevelopmentAdvice.AbilityScoreDispersion abilityScoreDispersion : abilityScoreDispersionList) {
            BigDecimal comprehensiveScore = abilityScoreDispersion.getComprehensiveScore();
            BigDecimal comprehensiveDispersion = abilityScoreDispersion.getComprehensiveDispersion();
            // 集中优势：指标分高，离散度低
            if (comprehensiveScore.compareTo(comprehensiveScoreAllAvg) >= 0 && comprehensiveDispersion.compareTo(comprehensiveDispersionAllAvg) < 0) {
                abilityScoreDispersion.setRegionName(DhrEaConstant.TeamDevelopmentAdviceRegion.CONCENTRATED_ADVANTAGE);
                developmentAdvice.getConcentratedAdvantage().add(abilityScoreDispersion.getAbilityName());
            }
            // 分散优势：指标分高，离散度高
            if (comprehensiveScore.compareTo(comprehensiveScoreAllAvg) >= 0 && comprehensiveDispersion.compareTo(comprehensiveDispersionAllAvg) >= 0) {
                abilityScoreDispersion.setRegionName(DhrEaConstant.TeamDevelopmentAdviceRegion.DISPERSION_ADVANTAGE);
                developmentAdvice.getDispersionAdvantage().add(abilityScoreDispersion.getAbilityName());
            }
            // 集中待发展：指标分低，离散度低
            if (comprehensiveScore.compareTo(comprehensiveScoreAllAvg) < 0 && comprehensiveDispersion.compareTo(comprehensiveDispersionAllAvg) < 0) {
                abilityScoreDispersion.setRegionName(DhrEaConstant.TeamDevelopmentAdviceRegion.CONCENTRATED_DEVELOPMENT);
                developmentAdvice.getConcentratedDevelopment().add(abilityScoreDispersion.getAbilityName());
            }
            // 离散待发展：指标分低，离散度高
            if (comprehensiveScore.compareTo(comprehensiveScoreAllAvg) < 0 && comprehensiveDispersion.compareTo(comprehensiveDispersionAllAvg) >= 0) {
                abilityScoreDispersion.setRegionName(DhrEaConstant.TeamDevelopmentAdviceRegion.DISPERSION_DEVELOPMENT);
                developmentAdvice.getDispersionDevelopment().add(abilityScoreDispersion.getAbilityName());
            }
        }
        developmentAdvice.setAbilityScoreDispersion(abilityScoreDispersionList);
        return developmentAdvice;
    }

    /**
     * 判断该活动是否可以生成团队报告
     *
     * @param request
     */
    private void generateTeamReportCheck(DhrEaObjectTeamReportRequest request) {
        // 判断团队报告至少需要2个样本
        List<String> empCodes = request.getEmpCodes();
        if (empCodes.size() < 2) {
            throw new CommRunException("dhr.talent.error.activity.report.2samples.required");
        }
        // 检查测评活动状态信息
        checkEaActivityInfo(request.getEaId(), DhrEaConstant.ReportType.TEAM_REPORT);
    }

    /**
     * 检查测评活动状态信息
     *
     * @param eaId
     * @param reportType
     */
    private void checkEaActivityInfo(Long eaId, String reportType) {
        // 判断所选评分量表的“生成360报告”状态
        DhrEaActivityInfo dhrEaActivityInfo = getById(eaId);
        DhrQdQuestionnaireInfo dhrQdQuestionnaireInfo = dhrQdQuestionnaireInfoService.get(dhrEaActivityInfo.getQdId());
        Long rsId = dhrQdQuestionnaireInfo.getRsId();
        DhrRsRatingScaleInfo dhrRsRatingScaleInfo = dhrRsRatingScaleInfoService.get(rsId);
        if (DhrEaConstant.NO.equals(dhrRsRatingScaleInfo.getReportStatus())) {
            if (DhrEaConstant.ReportType.TEAM_REPORT.equals(reportType)) {
                throw new CommRunException("dhr.talent.error.activity.report.not.support.team");
            } else {
                throw new CommRunException("dhr.talent.error.activity.report.not.support.personal");
            }
        }
        // 如活动未启用“本人”评价者类别，提示“该测评活动不支持生成生团队报告”
        List<DhrEaEvaluationWeightResponse> weightResponseList = dhrEaEvaluationWeightService.findListByEaId(eaId);
        for (DhrEaEvaluationWeightResponse weightResponse : weightResponseList) {
            if (DhrEaConstant.EvaluationType.BR_TYPE.equals(weightResponse.getEvaluationType()) && DhrEaConstant.DISABLED.equals(weightResponse.getEnabled())) {
                if (DhrEaConstant.ReportType.TEAM_REPORT.equals(reportType)) {
                    throw new CommRunException("dhr.talent.error.activity.report.not.support.team");
                } else {
                    throw new CommRunException("dhr.talent.error.activity.report.not.support.personal");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bpmCallback(Map<String, String> map) {
        String status = map.get("status");
        //通过流程
        if (DhrEaConstant.ProcessStatus.PASS.equals(status)) {
            String businessNoStr = map.get("businessNo");
            String[] split = businessNoStr.split(BUSINESS_NO_DELIMITER);
            if (split.length == 3) {
                // 解析参数
                Long eaId = Long.valueOf(split[0]);
                String empCode = split[1];
                // 复制审批临时表评价者数据到正式表中
                dhrEaEvaluationReviewerTempService.copy2Formal(eaId, empCode);
                // 更新活动相关统计
                dhrEaEvaluationReviewerService.updateNumAsync(eaId);
            }
        }
        return true;
    }

    /**
     * 更新评价中当前审批阶段
     *
     * @param evaluationObjectId
     * @param currentStage
     * @param processInstanceId
     * @return
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean updateEvaluationObjectCurrentStage(Long evaluationObjectId, String currentStage, String processInstanceId) {
        DhrEaEvaluationObject dhrEaEvaluationObject = new DhrEaEvaluationObject();
        dhrEaEvaluationObject.setId(evaluationObjectId);
        dhrEaEvaluationObject.setCurrentStage(currentStage);
        if (CharSequenceUtil.isNotBlank(processInstanceId)) {
            dhrEaEvaluationObject.setProcessInstanceId(processInstanceId);
        }
        dhrEaEvaluationObject.setRemark("");
        dhrEaEvaluationObject.setProcessStatus(DhrEaConstant.ApprovalStatus.SUCCESS);
        return dhrEaEvaluationObjectService.updateById(dhrEaEvaluationObject);
    }

    /**
     * 更新评价中当前审批阶段
     *
     * @param evaluationObjectId
     * @param message
     * @return
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean updateEvaluationObjectApprovalStatusFail(Long evaluationObjectId, String message) {
        DhrEaEvaluationObject dhrEaEvaluationObject = new DhrEaEvaluationObject();
        dhrEaEvaluationObject.setId(evaluationObjectId);
        dhrEaEvaluationObject.setProcessStatus(DhrEaConstant.ApprovalStatus.FAIL);
        dhrEaEvaluationObject.setRemark(message);
        return dhrEaEvaluationObjectService.updateById(dhrEaEvaluationObject);
    }

    /**
     * 更新活动测评状态
     *
     * @param id
     * @param status
     * @return
     */
    private boolean updateStatus(Long id, String status) {
        DhrEaActivityInfo dhrEaActivityInfo = new DhrEaActivityInfo();
        dhrEaActivityInfo.setId(id);
        dhrEaActivityInfo.setEaStatus(status);
        return updateById(dhrEaActivityInfo);
    }


    /**
     * 发起BPM 流程
     *
     * @param processScopeVO 流程详细信息
     * @param starter        发起人
     * @param applicant      申请者人
     * @param businessNo     业务编码
     * @param businessName   业务名称
     * @param nextAssignees  下个节点审批人
     * @return
     */
    private ResultVO<ProcessRuInfo> startBpmProcess(ProcessScopeVO processScopeVO, String starter, String starterName,
                                                    String applicant, String applicantName, String businessNo, String businessName
            , List<String> nextAssignees) {
        Map<String, Object> businessMap = Maps.newHashMap();
        businessMap.put(DhrEaConstant.BpmBusinessMessage.NEXT_ASSIGNEE, nextAssignees);
        ProcessFormRequest formRequest = new ProcessFormRequest();
        formRequest.setBusinessNo(businessNo);
        formRequest.setBusinessName(businessName);
        formRequest.setCompanyId(processScopeVO.getCompanyId());
        formRequest.setCompanyType(processScopeVO.getCompanyType());
        formRequest.setEname(applicantName);
        formRequest.setPernr(applicant);
        formRequest.setInitiator(starter);
        formRequest.setInitiatorName(starterName);
        formRequest.setKey(processScopeVO.getGuid());
        formRequest.setMenuCode(processScopeVO.getMenuCode());
        formRequest.setSubMenuCode(processScopeVO.getSubMenuCode());
        formRequest.setSourceSysCode(processScopeVO.getSourceSysCode());
        formRequest.setBusinessMap(businessMap);
        formRequest.setVariables(Maps.newHashMap());
        formRequest.setLaneList(Lists.newArrayList());
        formRequest.setPushDtoList(Lists.newArrayList());
        formRequest.setCompleteFirstNode("N");
        ResultVO<ProcessRuInfo> startResult = processInstancesProvider.start(formRequest);
        return startResult;
    }

    /**
     * 获取bpm远程调用数据
     *
     * @param result
     * @param <R>
     * @return
     */
    private <R> R remoteBpmHandle(ResultVO<R> result) {
        if (WebConstant.RESULT_CODE_200.equals(result.getCode())) {
            return result.getData();
        }
        return null;
    }


    /**
     * 从spring ioc容器获取本bean
     * 解决事物失效问题
     *
     * @return
     */
    private DhrEaActivityInfoService getThisBean() {
        return SpringUtil.getBean(this.getClass());
    }

    /**
     * 组装个人报告数据
     *
     * @param dhrEaEvaluationObject
     * @param abilityScoreMap
     * @param response
     */
    private String buildPersonalReportData(DhrEaEvaluationObject dhrEaEvaluationObject, Map<String, List<DhrEaReportAbilityScoreResponse>> abilityScoreMap,
                                           DhrEaObjectGenerateReportResponse response) {
        String empCode = dhrEaEvaluationObject.getEmpCode();
        List<DhrEaReportAbilityScoreResponse> abilityScoreList = abilityScoreMap.get(empCode);
        BeanUtil.copyProperties(dhrEaEvaluationObject, response);
        //2.报告第二部分-指标定义
        response.setIndicatorDefinitionList(BeanUtil.copyToList(abilityScoreList, DhrEaObjectGenerateReportResponse.IndicatorDefinition.class));
        //3.报告第三部分-概况
        List<Double> scoreList = abilityScoreList.stream().map(a -> a.getComprehensiveScore().doubleValue()).collect(Collectors.toList());
        List<DhrEaObjectGenerateReportResponse.OverviewInfo> overviewInfoList = abilityScoreList.stream().sorted(Comparator.comparing(DhrEaReportAbilityScoreResponse::getAbilityCode))
                .map(a -> {
                    DhrEaObjectGenerateReportResponse.OverviewInfo overviewInfo = new DhrEaObjectGenerateReportResponse.OverviewInfo();
                    BeanUtil.copyProperties(a, overviewInfo);
                    //计算80分位值
                    overviewInfo.setPercentileScore(BigDecimal.valueOf(ScoreUtil.percentile(scoreList, 0.8)));
                    return overviewInfo;
                }).collect(Collectors.toList());
        response.setOverviewInfoList(overviewInfoList);
        //4.报告第四部分
        //4.1 优势能力: 显示能力项评分数从高到底Top 3，且分数大于等于4，并显示对应能力项名称和定义
        List<DhrEaReportAbilityScoreResponse> advantageAbility = abilityScoreList.stream().filter(abilityScore -> abilityScore.getComprehensiveScore().compareTo(BigDecimal.valueOf(4)) >= 0)
                .sorted(Comparator.comparing(DhrEaReportAbilityScoreResponse::getComprehensiveScore).reversed()).limit(3).collect(Collectors.toList());
        response.setAdvantageAbilityList(BeanUtil.copyToList(advantageAbility, DhrEaObjectGenerateReportResponse.AdvantageAbility.class));
        //4.2 待发展能力: 显示能力项分数从低到高 Bottom 3，且分数小于等于3，并显示对应能力项名称和定义
        List<DhrEaReportAbilityScoreResponse> developAbility = abilityScoreList.stream().filter(abilityScore -> abilityScore.getComprehensiveScore().compareTo(BigDecimal.valueOf(3)) <= 0)
                .sorted(Comparator.comparing(DhrEaReportAbilityScoreResponse::getComprehensiveScore)).limit(3).collect(Collectors.toList());
        response.setDevelopAbilityList(BeanUtil.copyToList(developAbility, DhrEaObjectGenerateReportResponse.DevelopAbility.class));
        //5.报告第五部分
        //5.1 认知偏差-图表一: 显示自评比他评均分低，且差距（他评减自评大于0）从高到低的Top3
        List<DhrEaReportAbilityScoreResponse> cognitiveBiasOne = abilityScoreList.stream().peek(abilityScore -> abilityScore.setDeviationScore(abilityScore.getComprehensiveScore()
                .subtract(abilityScore.getMyselfScore()))).filter(abilityScore -> BigDecimal.ZERO.compareTo(abilityScore.getDeviationScore()) < 0)
                .sorted(Comparator.comparing(DhrEaReportAbilityScoreResponse::getDeviationScore).reversed()).limit(3).collect(Collectors.toList());
        response.setCognitiveBiasOneList(BeanUtil.copyToList(cognitiveBiasOne, DhrEaObjectGenerateReportResponse.CognitiveBiasOne.class));
        //5.2 认知偏差-图表二: 显示自评比他评均分高，且差距（自评减他评大于0）从高到低的Top3
        List<DhrEaReportAbilityScoreResponse> cognitiveBiasTwo = abilityScoreList.stream().peek(abilityScore -> abilityScore.setDeviationScore(abilityScore.getMyselfScore()
                .subtract(abilityScore.getComprehensiveScore()))).filter(abilityScore -> BigDecimal.ZERO.compareTo(abilityScore.getDeviationScore()) < 0)
                .sorted(Comparator.comparing(DhrEaReportAbilityScoreResponse::getDeviationScore).reversed()).limit(3).collect(Collectors.toList());
        response.setCognitiveBiasTwoList(BeanUtil.copyToList(cognitiveBiasTwo, DhrEaObjectGenerateReportResponse.CognitiveBiasTwo.class));
        //5.3 认知偏差-图表三: 各评价者类别分数，顺序从上到下为上级、平级、下级、其他
        response.setCognitiveBiasThreeList(BeanUtil.copyToList(abilityScoreList, DhrEaObjectGenerateReportResponse.CognitiveBiasThree.class));
        //6.发展建议
        this.buildDevelopSuggestionData(abilityScoreList, response);
        return JSONObject.toJSONString(response);

    }

    /**
     * 组装个人报告-发展建议数据
     *
     * @param abilityScoreList
     * @param response
     */
    private void buildDevelopSuggestionData(List<DhrEaReportAbilityScoreResponse> abilityScoreList, DhrEaObjectGenerateReportResponse response) {
        List<DhrEaReportAbilityScoreResponse> myselfScoreDesc = abilityScoreList.stream().sorted(Comparator.comparing(DhrEaReportAbilityScoreResponse::getMyselfScore).reversed())
                .collect(Collectors.toList());
        List<DhrEaReportAbilityScoreResponse> firstHalfMyselfScoreDesc = myselfScoreDesc.subList(0, myselfScoreDesc.size() / 2);
        List<DhrEaReportAbilityScoreResponse> lastHalfMyselfScoreDesc = myselfScoreDesc.subList(myselfScoreDesc.size() / 2, myselfScoreDesc.size());
        List<DhrEaReportAbilityScoreResponse> comprehensiveScoreDesc = abilityScoreList.stream().sorted(Comparator.comparing(DhrEaReportAbilityScoreResponse::getComprehensiveScore).reversed())
                .collect(Collectors.toList());
        List<DhrEaReportAbilityScoreResponse> firstHalfComprehensiveScoreDesc = comprehensiveScoreDesc.subList(0, comprehensiveScoreDesc.size() / 2);
        List<DhrEaReportAbilityScoreResponse> lastComprehensiveScoreDesc = comprehensiveScoreDesc.subList(comprehensiveScoreDesc.size() / 2, comprehensiveScoreDesc.size());
        List<DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo> developSuggestionInfo = new ArrayList<>();
        //6.1 优势共识区 自评0-50%（排名前50%的能力项），综合得分0-50%（排名前50%的能力项）
        DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo advantageArea = new DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo();
        advantageArea.setType(DhrEaConstant.DevelopSuggestionType.ADVANTAGE_AREA);
        List<DhrEaReportAbilityScoreResponse> advantageAreaList = firstHalfMyselfScoreDesc.stream().filter(firstHalfComprehensiveScoreDesc::contains).collect(Collectors.toList());
        advantageArea.setAbilityList(BeanUtil.copyToList(advantageAreaList, DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo.Ability.class));
        developSuggestionInfo.add(advantageArea);
        //6.2 潜能区 自评50-100%（排名50%以后的能力项），综合得分0-50%（排名前50%的能力项）
        DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo potentialArea = new DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo();
        potentialArea.setType(DhrEaConstant.DevelopSuggestionType.POTENTIAL_AREA);
        List<DhrEaReportAbilityScoreResponse> potentialAreaList = lastHalfMyselfScoreDesc.stream().filter(firstHalfComprehensiveScoreDesc::contains).collect(Collectors.toList());
        potentialArea.setAbilityList(BeanUtil.copyToList(potentialAreaList, DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo.Ability.class));
        developSuggestionInfo.add(potentialArea);
        //6.3 盲区 自评0-50%（排名前50%的能力项），综合得分50-100%（排名50%以后的能力项）
        DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo blindArea = new DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo();
        blindArea.setType(DhrEaConstant.DevelopSuggestionType.BLIND_AREA);
        List<DhrEaReportAbilityScoreResponse> blindAreaList = firstHalfMyselfScoreDesc.stream().filter(lastComprehensiveScoreDesc::contains).collect(Collectors.toList());
        blindArea.setAbilityList(BeanUtil.copyToList(blindAreaList, DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo.Ability.class));
        developSuggestionInfo.add(blindArea);
        //6.4 待发展共识区  自评50-100%（排名50%以后的能力项），综合得分50-100%（排名50%以后的能力项）
        DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo developedArea = new DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo();
        developedArea.setType(DhrEaConstant.DevelopSuggestionType.DEVELOPED_AREA);
        List<DhrEaReportAbilityScoreResponse> developedAreaList = lastHalfMyselfScoreDesc.stream().filter(lastComprehensiveScoreDesc::contains).collect(Collectors.toList());
        developedArea.setAbilityList(BeanUtil.copyToList(developedAreaList, DhrEaObjectGenerateReportResponse.DevelopSuggestionInfo.Ability.class));
        developSuggestionInfo.add(developedArea);
        response.setDevelopSuggestionInfoList(developSuggestionInfo);
    }

    /**
     * 生成报告文件
     *
     * @param reportDataKey
     * @param reportDataValue
     * @param templateName
     * @param fileName
     * @return
     */
    private FileResponseDto generateReportFile(String reportDataKey, String reportDataValue, String templateName, String fileName) {
        Map<String, Object> dataMap = new HashMap<>(16);
        dataMap.put(reportDataKey, reportDataValue);
        ReportHtmlTpl tpl = new ReportHtmlTpl(templateName, dataMap);
        byte[] fileBytes = reportTemplate.generateHtml(tpl);
        // HTML文件名:活动名称-评价对象工号-评价对象姓名-评价对象岗位.pdf
        String htmlFileName = fileName + ".html";
        ResponseVO<FileResponseDto> htmlFileResponse = commonFileDfsInterface.uploadFile(new FileRequestDto(htmlFileName, fileBytes));
        FileResponseDto fileData = htmlFileResponse.getData();
        // 进行渲染并转换pdf
        byte[] pdfBytes = reportTemplate.htmlConvertPdf(reportUrl + fileData.getId());
        String pdfFileName = fileName + ".pdf";
        ResponseVO<FileResponseDto> pdfFileResponse = commonFileDfsInterface.uploadFile(new FileRequestDto(pdfFileName, pdfBytes));
        // 本地debug开启
        // SeleniumUtil.quit();
        return pdfFileResponse.getData();
    }

    /**
     * 组装启用的类型和对应的分数属性字段名 例如 key：superiorScore value：上级
     *
     * @param weightList
     * @param evaluationTypeMap
     * @return
     */
    private Map<String, String> getEnableEvaluationTypeMap(List<DhrEaEvaluationWeight> weightList, Map<String, String> evaluationTypeMap) {
        Map<String, String> map = new LinkedHashMap<>();
        weightList.forEach(evaluationWeight -> {
            String paramName = "";
            switch (evaluationWeight.getEvaluationType()) {
                case "SJ":
                    paramName = "superiorScore";
                    break;
                case "PJ":
                    paramName = "sameLevelScore";
                    break;
                case "XJ":
                    paramName = "lowerLevelScore";
                    break;
                case "QT":
                    paramName = "otherScore";
                    break;
                default:
            }
            if (CharSequenceUtil.isBlank(paramName)) {
                return;
            }
            map.put(paramName, evaluationTypeMap.get(evaluationWeight.getEvaluationType()));
        });
        return map;
    }

}

