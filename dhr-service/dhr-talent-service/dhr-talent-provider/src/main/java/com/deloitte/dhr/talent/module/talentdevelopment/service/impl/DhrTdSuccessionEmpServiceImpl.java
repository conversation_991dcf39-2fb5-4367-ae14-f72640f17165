package com.deloitte.dhr.talent.module.talentdevelopment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deloitte.dhr.common.Request;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.ResponseVO;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.common.util.CopyUtil;
import com.deloitte.dhr.excel.parser.handler.I18nCellWriteHandler;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheet;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheetData;
import com.deloitte.dhr.excel.parser.template.ExcelParserTemplate;
import com.deloitte.dhr.excel.parser.util.MessageUtil;
import com.deloitte.dhr.excel.parser.util.RequestContextUtil;
import com.deloitte.dhr.mda.module.base.EmployeeFromPersonnelWarehouseFeignInterface;
import com.deloitte.dhr.mda.module.base.OrganizationFeignInterface;
import com.deloitte.dhr.mda.module.base.PositionTalentFeignInterface;
import com.deloitte.dhr.mda.module.base.pojo.*;
import com.deloitte.dhr.talent.module.talentdevelopment.constant.DhrDeConstant;
import com.deloitte.dhr.talent.module.talentdevelopment.domain.DhrTdSucOperateRecord;
import com.deloitte.dhr.talent.module.talentdevelopment.domain.DhrTdSuccessionEmp;
import com.deloitte.dhr.talent.module.talentdevelopment.excel.dto.TdSuccessionEmpDTO;
import com.deloitte.dhr.talent.module.talentdevelopment.mapper.DhrTdSuccessionEmpMapper;
import com.deloitte.dhr.talent.module.talentdevelopment.pojo.DhrTdPositionTalentResponse;
import com.deloitte.dhr.talent.module.talentdevelopment.pojo.DhrTdSuccessionEmpRequest;
import com.deloitte.dhr.talent.module.talentdevelopment.pojo.DhrTdSuccessionEmpResponse;
import com.deloitte.dhr.talent.module.talentdevelopment.service.AsyncService;
import com.deloitte.dhr.talent.module.talentdevelopment.service.DhrTdSucOperateRecordService;
import com.deloitte.dhr.talent.module.talentdevelopment.service.DhrTdSuccessionEmpService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Comment：dhr_td_succession_emp继任人
 *
 * @author： chaxiang
 * Date： 2022-09-02
 */
@Service
public class DhrTdSuccessionEmpServiceImpl extends SuperServiceImpl<DhrTdSuccessionEmpMapper, DhrTdSuccessionEmp> implements DhrTdSuccessionEmpService {

    @Autowired
    private DhrTdSucOperateRecordService dhrTdSucOperateRecordService;
    @Autowired
    PositionTalentFeignInterface positionTalentFeignInterface;
    @Autowired
    DhrTdSuccessionEmpMapper dhrTdSuccessionEmpMapper;
    @Autowired
    OrganizationFeignInterface organizationFeignInterface;
    @Autowired
    EmployeeFromPersonnelWarehouseFeignInterface employeeFromPersonnelWarehouseFeignInterface;
    @Autowired
    private AsyncService asyncService;
    @Resource
    private ExcelParserTemplate excelParserTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDhrTdSuccessionEmp(DhrTdSuccessionEmp dhrTdSuccessionEmp) {
        boolean insert = false;
        if (isExist(dhrTdSuccessionEmp)) {
            throw new CommRunException("dhr.mdb.db.error.dhrTdSuccessionEmpIsExists");
        } else {
            insert = this.insert(dhrTdSuccessionEmp);
            insertRecord(dhrTdSuccessionEmp, "ADD");
        }
        return insert;
    }

    private boolean isExist(DhrTdSuccessionEmp dhrTdSuccessionEmp) {
        QueryWrapper<DhrTdSuccessionEmp> query = new QueryWrapper<>();
        query.eq("position_code", dhrTdSuccessionEmp.getPositionCode());
        query.eq("deleted", 0);
        query.eq("emp_type", dhrTdSuccessionEmp.getEmpType());
        if (dhrTdSuccessionEmp.getId() != null) {
            query.ne("id", dhrTdSuccessionEmp.getId());
        }
        if (dhrTdSuccessionEmp.getEmpType() != null) {
            if (DhrDeConstant.QuestionType.OUTSIDE.equalsIgnoreCase(dhrTdSuccessionEmp.getEmpType())) {
                query.eq("emp_name", dhrTdSuccessionEmp.getEmpName());
            } else if (DhrDeConstant.QuestionType.INSIDE.equalsIgnoreCase(dhrTdSuccessionEmp.getEmpType())) {
                query.eq("emp_code", dhrTdSuccessionEmp.getEmpCode());
            }
        }
        Long count = dhrTdSuccessionEmpMapper.selectCount(query);
        //该继任者已在在任者列表中
        List<DhrBscEmployeeOrgInfoResp> codes =
                organizationFeignInterface.getEmployeeOrgByCodes(
                        Collections.singletonList(dhrTdSuccessionEmp.getPositionCode()))
                        .getData();
        if (!CollectionUtils.isEmpty(codes)) {
            List<String> empCode = codes.stream()
                    .filter(o -> StringUtils.equals(o.getPosCode(), dhrTdSuccessionEmp.getPositionCode()))
                    .map(DhrBscEmployeeOrgInfoResp::getEmpCode).collect(Collectors.toList());
            if (empCode.contains(dhrTdSuccessionEmp.getEmpCode())) {
                throw new CommRunException("dhr.mdb.db.error.empHired");
            }
        }
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDhrTdSuccessionEmp(DhrTdSuccessionEmp dhrTdSuccessionEmp) {
        boolean update = false;
        if (isExist(dhrTdSuccessionEmp)) {
            throw new CommRunException("dhr.mdb.db.error.dhrTdSuccessionEmpIsExists");
        } else {
            update = this.update(dhrTdSuccessionEmp);
            insertRecord(dhrTdSuccessionEmp, "UPDATE");
        }
        return update;
    }

    @Override
    public boolean deleteDhrTdSuccessionEmp(List<Long> ids) {
        DhrTdSuccessionEmpRequest request = new DhrTdSuccessionEmpRequest();
        request.setIds(ids);
        QueryWrapper<DhrTdSuccessionEmp> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        List<DhrTdSuccessionEmp> list = this.list(queryWrapper);
        List<DhrTdSucOperateRecord> recordList = list.stream().map(dhrTdSuccessionEmpResponse -> generateRecord(BeanUtil.copyProperties(dhrTdSuccessionEmpResponse, DhrTdSuccessionEmp.class), "DELETE")).collect(Collectors.toList());
        dhrTdSucOperateRecordService.saveBatch(recordList);
        return this.delete(ids);
    }

    private DhrTdSucOperateRecord generateRecord(DhrTdSuccessionEmp dhrTdSuccessionEmp, String operateType) {
        UserDto user = LoginUtil.getLoginUser();
        DhrTdSucOperateRecord record = new DhrTdSucOperateRecord();
        record.setPositionCode(dhrTdSuccessionEmp.getPositionCode());
        record.setOperateType(operateType);
        record.setOperateTime(new Date());
        record.setReadiness(dhrTdSuccessionEmp.getReadiness());
        record.setSucCode(dhrTdSuccessionEmp.getEmpCode());
        record.setSucName(dhrTdSuccessionEmp.getEmpName());
        record.setRemark(dhrTdSuccessionEmp.getRemark());
        if (user != null) {
            record.setOperateCode(user.getEmployeeNumber());
            record.setOperateName(user.getFullname());
        }
        return record;
    }

    public boolean insertRecord(DhrTdSuccessionEmp dhrTdSuccessionEmp, String operateType) {
        return dhrTdSucOperateRecordService.insert(generateRecord(dhrTdSuccessionEmp, operateType));
    }

    @Override
    public ResponseVO<ResponsePage<DhrTdPositionTalentResponse>> listPositions(Long current, Long size, Request<PositionTalentRequest> positionTalentRequestRequest) {
        ResponseVO<ResponsePage<PositionTalentResponse>> responseVO = positionTalentFeignInterface.list(current, size, positionTalentRequestRequest);
        if (responseVO != null && responseVO.getData() != null) {
            ResponsePage<PositionTalentResponse> responsePage = responseVO.getData();
            List<PositionTalentResponse> list = responsePage.getDataList();
            if (!CollectionUtils.isEmpty(list)) {
                List<String> orgCodes = list.stream().map(PositionTalentResponse::getOrgCode).collect(Collectors.toList());
                List<OrganizationBasicInfoResponse> organizationBasicInfoResponseVO = organizationFeignInterface.getOrgByCodes(orgCodes).getData();
                Map<String, String> orgMap = new HashMap<>(16);
                List<String> empCodeList = new ArrayList<>();
                list.stream().forEach(position -> {
                    String empCodes = position.getEmpCodes();
                    if (empCodes != null) {
                        empCodeList.addAll(Arrays.asList(empCodes.split(",")));
                    }
                });
                Map<String, EmployeeFromPersonnelWarehouseResponse> empMap = CollectionUtils.isEmpty(empCodeList) ? new HashMap<>(16) : getEmps(empCodeList);
                organizationBasicInfoResponseVO.stream().forEach(org -> orgMap.put(org.getOrgCode(), org.getOrgName()));
                //岗位编码集合
                List<String> positionCodes = list.stream().map(p -> p.getPositionCode()).collect(Collectors.toList());
                QueryWrapper<DhrTdSuccessionEmp> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("position_code", positionCodes);
                queryWrapper.orderByAsc("(case readiness when 'READY' then 1 when 'IN_ONE_YEAR' then 2 when 'ONE_TO_TWO_YEARS' then 3 when 'MORE_THAN_TWO_YEARS' then 4 else 5 end)");
                queryWrapper.orderByAsc("emp_type");
                queryWrapper.orderByAsc("emp_code");
                List<DhrTdSuccessionEmp> dhrTdSuccessionEmpList = dhrTdSuccessionEmpMapper.selectList(queryWrapper);
                //根据岗位编码分组
                Map<String, List<DhrTdSuccessionEmp>> dhrTdSuccessionEmpMap = dhrTdSuccessionEmpList.stream()
                        .collect(Collectors.groupingBy(DhrTdSuccessionEmp::getPositionCode));
                List<DhrTdPositionTalentResponse> listResponse = list.stream().map(position -> {
                    DhrTdPositionTalentResponse positionTalentResponse = new DhrTdPositionTalentResponse();
                    CopyUtil.copyProperties(position, positionTalentResponse);
                    List<DhrTdSuccessionEmp> successionEmpList = dhrTdSuccessionEmpMap.get(position.getPositionCode()) == null ?
                            Lists.newArrayList() : dhrTdSuccessionEmpMap.get(position.getPositionCode());
                    positionTalentResponse.setPrepareWorkEmp(successionEmpList.stream().map(dhrTdSuccessionEmp -> {
                        DhrTdSuccessionEmpResponse successionEmpResponse = new DhrTdSuccessionEmpResponse();
                        CopyUtil.copyProperties(dhrTdSuccessionEmp, successionEmpResponse);
                        return successionEmpResponse;
                    }).collect(Collectors.toList()));
                    if (position.getEmpCodes() != null) {
                        List<String> curEmpCodes = Arrays.asList(position.getEmpCodes().split(","));
                        positionTalentResponse.setInWorkEmp(curEmpCodes.stream().map(empCode -> empMap.get(empCode)).collect(Collectors.toList()));
                    }
                    positionTalentResponse.setOrgName(orgMap.get(positionTalentResponse.getOrgCode()));
                    return positionTalentResponse;
                }).collect(Collectors.toList());
                return ResponseVO.success(new ResponsePage<>(responsePage.getPageNum(), responsePage.getPageSize(), responsePage.getTotalCount(), listResponse));
            }
        }
        return ResponseVO.success(null);
    }

    @Override
    public void export(Request<PositionTalentRequest> request) {
        // 获取数据
        Long count = positionTalentFeignInterface.count(request).getData();
        //获取下级组织codes(包含当前组织code)
        List<String> orgCodes = positionTalentFeignInterface.generateOrgCodes(request.get().getOrgCode(), request.get().isContainChildren()).getData();
        request.get().setOrgCodes(orgCodes);
        Long pageSize = (long) Math.ceil(count / Double.valueOf(500));
        List<Future<List<DhrTdPositionTalentResponse>>> futures = Lists.newArrayList();
        List<DhrTdPositionTalentResponse> dataList = Lists.newArrayList();
        try {
            for (long i = 1; i <= pageSize; i++) {
                Future<List<DhrTdPositionTalentResponse>> futureList = asyncService.listPositions(i, 500L, request
                        , SecurityContextHolder.getContext(), RequestContextUtil.getHeaderMap());
                futures.add(futureList);
            }
            for (Future<List<DhrTdPositionTalentResponse>> future : futures) {
                dataList.addAll(future.get());
            }
        } catch (InterruptedException e) {
            log.error(e.toString());
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error(e.toString());
        }
        List<TdSuccessionEmpDTO> list = new ArrayList<>();
        if (dataList != null) {
            for (DhrTdPositionTalentResponse item : dataList) {
                // 获取在任者数据
                list.addAll(getInWorkEmp2DTO(item));
                // 获取继任者数据
                list.addAll(getPrepareWorkEmp2DTO(item));
            }
        }
        // 导出excel
        ExcelSheetData<TdSuccessionEmpDTO> excelSheetData = new ExcelSheetData<>(TdSuccessionEmpDTO.class, list);
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(MessageUtil.getText("export.name.dhr.talentdevelopment.dhrTdSuccessionEmp") + DateUtil.now() + ".xlsx", CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), null);
    }

    /**
     * 获取在任者数据（转换到导出DTO）
     *
     * @param item
     * @return
     */
    private List<TdSuccessionEmpDTO> getInWorkEmp2DTO(DhrTdPositionTalentResponse item) {
        List<TdSuccessionEmpDTO> list = new ArrayList<>();
        List<EmployeeFromPersonnelWarehouseResponse> inWorkEmp = item.getInWorkEmp();
        if (CollUtil.isNotEmpty(inWorkEmp)) {
            Map<String, List<EmployeeFromPersonnelWarehouseResponse>> workerMap = inWorkEmp.stream()
                    .collect(Collectors.groupingBy(EmployeeFromPersonnelWarehouseResponse::getOrgCode));
            workerMap.forEach((s, inWorkEmpItem) -> {
                TdSuccessionEmpDTO tdSuccessionEmpDTO = new TdSuccessionEmpDTO();
                tdSuccessionEmpDTO.setOrgCode(item.getOrgCode());
                tdSuccessionEmpDTO.setOrgName(item.getOrgName());
                tdSuccessionEmpDTO.setPositionCode(item.getPositionCode());
                tdSuccessionEmpDTO.setPositionName(item.getPositionDesc());
                String name = inWorkEmpItem.stream()
                        .map(o -> (o.getEmpCode() + " " + o.getFullname()))
                        .collect(Collectors.joining(","));
                tdSuccessionEmpDTO.setInWorkEmpName(name);
                list.add(tdSuccessionEmpDTO);
            });
        }
        return list;
    }

    /**
     * 获取继任者数据（转换到导出DTO）
     *
     * @param item
     * @return
     */
    private List<TdSuccessionEmpDTO> getPrepareWorkEmp2DTO(DhrTdPositionTalentResponse item) {
        List<TdSuccessionEmpDTO> list = new ArrayList<>();
        List<DhrTdSuccessionEmpResponse> prepareWorkEmp = item.getPrepareWorkEmp();
        if (CollUtil.isNotEmpty(prepareWorkEmp)) {
            for (DhrTdSuccessionEmpResponse prepareWorkEmpItem : prepareWorkEmp) {
                TdSuccessionEmpDTO tdSuccessionEmpDTO = new TdSuccessionEmpDTO();
                tdSuccessionEmpDTO.setOrgCode(item.getOrgCode());
                tdSuccessionEmpDTO.setOrgName(item.getOrgName());
                tdSuccessionEmpDTO.setPositionCode(item.getPositionCode());
                tdSuccessionEmpDTO.setPositionName(item.getPositionDesc());
                tdSuccessionEmpDTO.setPrepareWorkEmpCode(prepareWorkEmpItem.getEmpCode());
                tdSuccessionEmpDTO.setPrepareWorkEmpName(prepareWorkEmpItem.getEmpName());
                tdSuccessionEmpDTO.setReadiness(prepareWorkEmpItem.getReadiness());
                tdSuccessionEmpDTO.setRemark(prepareWorkEmpItem.getRemark());
                list.add(tdSuccessionEmpDTO);
            }
        }
        return list;
    }

    @Override
    public Map<String, EmployeeFromPersonnelWarehouseResponse> getEmps(List<String> empCodes) {
        Map<String, EmployeeFromPersonnelWarehouseResponse> empMap = new HashMap<>(16);
        Request<EmployeeFromPersonnelWarehouseRequest> request = new Request<>();
        EmployeeFromPersonnelWarehouseRequest employee = new EmployeeFromPersonnelWarehouseRequest();
        employee.setEmpCodes(empCodes);
        employee.setEngageStatus(3);
        request.setParam(employee);
        ResponseVO<ResponsePage<EmployeeFromPersonnelWarehouseResponse>> responseVO = employeeFromPersonnelWarehouseFeignInterface.listEmp(null, 1L, -1L, request);
        if (responseVO != null && responseVO.getData() != null && !CollectionUtils.isEmpty(responseVO.getData().getDataList())) {
            responseVO.getData().getDataList().stream().forEach(employee1 -> empMap.put(employee1.getEmpCode(), employee1));
        }
        return empMap;
    }
}

