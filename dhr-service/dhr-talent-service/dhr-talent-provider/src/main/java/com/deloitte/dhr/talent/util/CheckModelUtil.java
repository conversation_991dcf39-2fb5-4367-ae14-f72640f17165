package com.deloitte.dhr.talent.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.google.common.collect.Maps;


import java.util.*;

/**
 * Model数据校验工具类
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
public class CheckModelUtil {

    /**
     * 检查批量保存数据重复性
     *
     * @param mapper   mapper对象
     * @param dataList 数据集合
     * @param field    字段，and关系
     * @param <R>
     * @return
     */
    @SafeVarargs
    public static final <R> Set<R> checkSameDateBatch(BaseMapper mapper, List<R> dataList, SFunction<R, ?>... field) {
        return checkSameDateBatch(mapper, dataList, false, field);
    }

    /**
     * 检查批量保存、修改数据重复性
     *
     * @param mapper   mapper对象
     * @param dataList 数据集合
     * @param isUpdate 是否修改情况
     * @param field    字段，and关系
     * @param <R>
     * @return
     */
    @SafeVarargs
    public static final <R> Set<R> checkSameDateBatch(BaseMapper mapper, List<R> dataList, boolean isUpdate, SFunction<R, ?>... field) {
        // 内存中参数检查
        Set<R> elementSet = distinctParameter(dataList, field);
        // 数据库中检查
        dataList.forEach(data -> {
            if (checkSameDate(mapper, isUpdate, data, field)) {
                elementSet.add(data);
            }
        });
        return elementSet;
    }

    /**
     * 解析重复数据提示内容
     *
     * @param elementSet   重复数据集合
     * @param spliceSymbol 分割符号
     * @param field        取值字段
     * @param <R>
     * @return
     */
    public static <R> Set<String> parsePrompt(Set<R> elementSet, String spliceSymbol, SFunction<R, ?>... field) {
        Set<String> strSet = new HashSet<>();
        if (elementSet.size() > 0) {
            elementSet.forEach(item -> {
                StringBuffer strBuf = new StringBuffer();
                for (int i = 0; i < field.length; i++) {
                    if (i == field.length - 1) {
                        strBuf.append(field[i].apply(item));
                    } else {
                        strBuf.append(field[i].apply(item) + spliceSymbol);
                    }
                }
                strSet.add(strBuf.toString());
            });
        }
        return strSet;
    }

    /**
     * 单数据检查重复
     *
     * @param mapper mapper对象
     * @param data   数据集合
     * @param field  字段
     * @param <R>
     */
    @SafeVarargs
    public static final <R> boolean checkSameDate(BaseMapper mapper, R data, SFunction<R, ?>... field) {
        return checkSameDate(mapper, false, data, field);
    }

    /**
     * 单数据检查重复
     *
     * @param mapper   mapper对象
     * @param isUpdate 是否修改场景
     * @param data     数据集合
     * @param field    字段
     * @param <R>
     */
    @SafeVarargs
    public static final <R> boolean checkSameDate(BaseMapper mapper, boolean isUpdate, R data, SFunction<R, ?>... field) {
        Map<String, Object> map = Maps.newHashMap();
        Arrays.stream(field).forEach(s -> {
            map.put(getKey(s), s.apply(data));
        });
        Map<String, Object> dataMap = BeanUtil.beanToMap(data);
        return selectSameDate(mapper, map, dataMap, isUpdate);
    }

    /**
     * 判断某一系列字段是否存在重复项
     *
     * @param mapper
     * @param map
     * @param dataMap
     * @param isUpdate
     */
    private static boolean selectSameDate(BaseMapper mapper, Map<String, Object> map, Map<String, Object> dataMap, boolean isUpdate) {
        QueryWrapper wrapper = new QueryWrapper<>();
        map.forEach((k, v) -> wrapper.eq(StrUtil.toUnderlineCase(k), v));
        if (isUpdate) {
            // 默认ID字段：id,修改场景排除当前数据
            wrapper.ne("id", dataMap.get("id"));
        }
        // 如果实体数据中有 deleted 字段，则过滤逻辑删除
        if (dataMap.containsKey("deleted")) {
            // not deleted
            wrapper.eq("deleted", false);
        }
        Long count = mapper.selectCount(wrapper);
        return count > 0;
    }

    /**
     * 检查参数是否重复
     *
     * @param list  数据集合
     * @param field 字段
     * @param <R>
     * @return
     */
    public static <R> Set<R> distinctParameter(List<R> list, SFunction<R, ?>... field) {
        Set<R> elementSet = new HashSet<>();
        Map<String, Integer> map = Maps.newHashMap();
        for (R item : list) {
            StringBuffer strBuf = new StringBuffer();
            Arrays.stream(field).forEach(s -> strBuf.append(s.apply(item)));
            String key = SecureUtil.md5(strBuf.toString());
            // 检测key是否重复
            if (map.containsKey(key)) {
                Integer num = map.get(key);
                map.put(key, num + 1);
                elementSet.add(item);
            } else {
                map.put(key, 1);
            }
        }
        return elementSet;
    }

    /**
     * 获取属性名
     *
     * @param column
     * @return
     */
    private static <R> String getKey(SFunction<R, ?> column) {
        // 由于MyBatis Plus版本变化，暂时使用简化的实现
        // 这个方法主要用于获取Lambda表达式对应的属性名
        // 在实际使用中，可能需要根据具体的MyBatis Plus版本调整实现
        throw new UnsupportedOperationException("getKey method needs to be updated for current MyBatis Plus version");
    }
}
