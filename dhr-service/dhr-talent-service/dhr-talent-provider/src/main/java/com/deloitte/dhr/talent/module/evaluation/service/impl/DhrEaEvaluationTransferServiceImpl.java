package com.deloitte.dhr.talent.module.evaluation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.talent.module.evaluation.constant.DhrEaConstant;
import com.deloitte.dhr.talent.module.evaluation.domain.DhrEaEvaluationReviewer;
import com.deloitte.dhr.talent.module.evaluation.domain.DhrEaEvaluationTransfer;
import com.deloitte.dhr.talent.module.evaluation.domain.DhrEaObjReviewerRel;
import com.deloitte.dhr.talent.module.evaluation.mapper.DhrEaEvaluationTransferMapper;
import com.deloitte.dhr.talent.module.evaluation.pojo.DhrEaAnswerManageSearchRequest;
import com.deloitte.dhr.talent.module.evaluation.pojo.DhrEaEvaluationReviewerTransferRequest;
import com.deloitte.dhr.talent.module.evaluation.pojo.DhrEaEvaluationTransferRequest;
import com.deloitte.dhr.talent.module.evaluation.pojo.DhrEaEvaluationTransferResponse;
import com.deloitte.dhr.talent.module.evaluation.service.DhrEaEvaluationReviewerService;
import com.deloitte.dhr.talent.module.evaluation.service.DhrEaEvaluationTransferService;
import com.deloitte.dhr.talent.module.evaluation.service.DhrEaEvaluatorAsyncService;
import com.deloitte.dhr.talent.module.evaluation.service.DhrEaObjReviewerRelService;
import com.deloitte.dhr.talent.util.TalentFeignMethodUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Comment：dhr_ea_evaluation_transfer360测评 - 测评活动管理 - 转办记录表
 *
 * <AUTHOR>
 * @date 2022-09-19
 */
@Service
public class DhrEaEvaluationTransferServiceImpl extends SuperServiceImpl<DhrEaEvaluationTransferMapper, DhrEaEvaluationTransfer> implements DhrEaEvaluationTransferService {

    @Resource
    private DhrEaEvaluationTransferMapper dhrEaEvaluationTransferMapper;

    @Autowired
    private DhrEaEvaluationReviewerService dhrEaEvaluationReviewerService;

    @Autowired
    private DhrEaObjReviewerRelService dhrEaObjReviewerRelService;

    @Autowired
    private DhrEaEvaluatorAsyncService dhrEaEvaluatorAsyncService;
    @Autowired
    private TalentFeignMethodUtil talentFeignMethodUtil;

    @Override
    public ResponsePage<DhrEaEvaluationTransferResponse> transferPageList(Page<DhrEaEvaluationTransfer> page, DhrEaAnswerManageSearchRequest request, BaseOrder order) {
        List<DhrEaEvaluationTransferResponse> list = dhrEaEvaluationTransferMapper.transferPageList(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferSend(DhrEaEvaluationTransferRequest request) {
        // 保存转办记录
        DhrEaEvaluationReviewerTransferRequest evaluationReviewerTransferRequest = request.getEvaluationReviewerTransferRequest();
        String transferEvaluationReviewer = evaluationReviewerTransferRequest.getEmpCode();
        if (request.getEvaluationReviewer().equals(transferEvaluationReviewer)) {
            throw new CommRunException("dhr.talent.error.evaluatorTransferObjectSame");
        }
        //保存转办信息
        this.saveEvaluationTransfer(request);
        // 更改原评价者与测评对象的关系：变更为转办对象
        // 判断该转办者是否在该活动评价者中，如果不存在则新增
        DhrEaEvaluationReviewer eaEvaluationReviewer = dhrEaEvaluationReviewerService.getEaEvaluationReviewer(request.getEaId(), transferEvaluationReviewer);
        if (eaEvaluationReviewer == null) {
            DhrEaEvaluationReviewer reviewer = BeanUtil.copyProperties(evaluationReviewerTransferRequest, DhrEaEvaluationReviewer.class);
            reviewer.setEmpEvaluationObjCount(0);
            reviewer.setEvaluationFinished(0);
            reviewer.setEvaluationUnfinished(0);
            reviewer.setAnswerStatus(DhrEaConstant.AnswerStatus.AN_STATUS_WDT);
            dhrEaEvaluationReviewerService.save(reviewer);
        } else {
            // 该转办对象已存在与活动中且答案已作废，不允许进行该操作
            if (DhrEaConstant.AnswerStatus.AN_STATUS_ZF.equals(eaEvaluationReviewer.getAnswerStatus())) {
                throw new CommRunException("dhr.talent.error.activity.transfer.object.outOfDate");
            }
        }
        // 更新关系
        List<DhrEaObjReviewerRel> dhrEaObjReviewerRels = getDhrEaObjReviewerRel(request.getEaId(), request.getEvaluationReviewer());
        List<DhrEaObjReviewerRel> updateRelList = new ArrayList<>();
        // 获取目标转办对象的关系
        List<DhrEaObjReviewerRel> transferDhrEaObjReviewerRels = getDhrEaObjReviewerRel(request.getEaId(), transferEvaluationReviewer);
        List<String> transferObjectCodes = transferDhrEaObjReviewerRels.stream().map(DhrEaObjReviewerRel::getEvaluationObjectCode).collect(Collectors.toList());
        Boolean isDeleteOldReviewer = Boolean.TRUE;
        for (DhrEaObjReviewerRel item : dhrEaObjReviewerRels) {
            // 未答题，答题中变更关系
            if (DhrEaConstant.AnswerStatus.AN_STATUS_WDT.equals(item.getAnswerStatus()) || DhrEaConstant.AnswerStatus.AN_STATUS_DTZ.equals(item.getAnswerStatus())) {
                // 如果转办对象已与被转办者存在关系，则不更新关系,仅删除原有关系
                if (transferObjectCodes.contains(item.getEvaluationObjectCode())) {
                    dhrEaObjReviewerRelService.getBaseMapper().deleteById(item.getId());
                } else {
                    item.setEvaluationReviewerCode(transferEvaluationReviewer);
                    updateRelList.add(item);
                }
            } else if (DhrEaConstant.AnswerStatus.AN_STATUS_YDT.equals(item.getAnswerStatus())) {
                // 如果存在已答题，则不用删除该原转办对象（评价者）
                isDeleteOldReviewer = Boolean.FALSE;
            }
        }
        // 判断原转办对象是否还在活动中没有任何测评对象，则删除（如果存在已答题，则不用删除该原转办对象（评价者））
        if (isDeleteOldReviewer) {
            LambdaQueryWrapper<DhrEaEvaluationReviewer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DhrEaEvaluationReviewer::getEaId, request.getEaId());
            wrapper.eq(DhrEaEvaluationReviewer::getEmpCode, request.getEvaluationReviewer());
            dhrEaEvaluationReviewerService.getBaseMapper().delete(wrapper);
        }
        // 判断原转办对象是否已在活动中未转办者，则清理记录
        clearOldTransfer(request.getEaId(), request.getEvaluationReviewer());
        // 执行更新
        boolean result = dhrEaObjReviewerRelService.updateBatchById(updateRelList);
        // 更新统计评价者的统计
        dhrEaEvaluationReviewerService.updateNumAsync(request.getEaId());
        return result;
    }

    /**
     * 保存转办对数据
     *
     * @param request
     */
    private void saveEvaluationTransfer(DhrEaEvaluationTransferRequest request) {
        DhrEaEvaluationTransfer dhrEaEvaluationTransfer = BeanUtil.copyProperties(request, DhrEaEvaluationTransfer.class);
        DhrEaEvaluationReviewer oldEvaluationReviewer = dhrEaEvaluationReviewerService.getEaEvaluationReviewer(request.getEaId(), request.getEvaluationReviewer());
        dhrEaEvaluationTransfer.setTransferEvaluationReviewer(request.getEvaluationReviewerTransferRequest().getEmpCode());
        dhrEaEvaluationTransfer.setEaReviewerOaCode(oldEvaluationReviewer.getOaCode());
        dhrEaEvaluationTransfer.setEaReviewerName(oldEvaluationReviewer.getEmpName());
        dhrEaEvaluationTransfer.setEaReviewerOrgFullPath(oldEvaluationReviewer.getEmpOrgFullPath());
        dhrEaEvaluationTransfer.setEaReviewerPositionName(oldEvaluationReviewer.getEmpPositionName());
        dhrEaEvaluationTransfer.setEaReviewerStatus(oldEvaluationReviewer.getEmpStatus());
        dhrEaEvaluationTransfer.setTransferEaReviewerName(request.getEvaluationReviewerTransferRequest().getEmpName());
        this.save(dhrEaEvaluationTransfer);
    }

    /**
     * 清理旧转办者
     *
     * @param eaId
     * @param reviewerCode
     */
    private void clearOldTransfer(Long eaId, String reviewerCode) {
        LambdaQueryWrapper<DhrEaEvaluationTransfer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DhrEaEvaluationTransfer::getEaId, eaId);
        wrapper.eq(DhrEaEvaluationTransfer::getTransferEvaluationReviewer, reviewerCode);
        dhrEaEvaluationTransferMapper.delete(wrapper);
    }

    /**
     * 通过评价者获取与所有测评对象的关系
     *
     * @param eaId
     * @param reviewerCode
     * @return
     */
    private List<DhrEaObjReviewerRel> getDhrEaObjReviewerRel(Long eaId, String reviewerCode) {
        LambdaQueryWrapper<DhrEaObjReviewerRel> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(DhrEaObjReviewerRel::getEaId, eaId);
        queryWrapper.eq(DhrEaObjReviewerRel::getEvaluationReviewerCode, reviewerCode);
        return dhrEaObjReviewerRelService.getBaseMapper().selectList(queryWrapper);
    }


    @Override
    public Boolean isTransferByEmpCode(Long eaId, String empCode) {
        LambdaQueryWrapper<DhrEaEvaluationTransfer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DhrEaEvaluationTransfer::getTransferEvaluationReviewer, empCode);
        queryWrapper.eq(DhrEaEvaluationTransfer::getEaId, eaId);
        Long total = dhrEaEvaluationTransferMapper.selectCount(queryWrapper);
        return total > 0;
    }
}

