package com.deloitte.dhr.talent.module.evaluation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.talent.module.evaluation.constant.DhrEaConstant;
import com.deloitte.dhr.talent.module.evaluation.domain.DhrQdDimensionalityTqRel;
import com.deloitte.dhr.talent.module.evaluation.mapper.DhrQdDimensionalityTqRelMapper;
import com.deloitte.dhr.talent.module.evaluation.pojo.*;
import com.deloitte.dhr.talent.module.evaluation.service.DhrQdDimensionalityTqRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Comment：dhr_qd_dimensionality_tq_rel360测评 - 问卷管理 - 维度与试题关系表
 *
 * <AUTHOR>
 * @date 2022-09-14
 */
@Service
public class DhrQdDimensionalityTqRelServiceImpl extends SuperServiceImpl<DhrQdDimensionalityTqRelMapper, DhrQdDimensionalityTqRel> implements DhrQdDimensionalityTqRelService {
    @Autowired
    private DhrQdDimensionalityTqRelMapper dhrQdDimensionalityTqRelMapper;

    @Override
    public void deleteByQdIds(List<Long> qdIds) {
        LambdaQueryWrapper<DhrQdDimensionalityTqRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DhrQdDimensionalityTqRel::getQdId, qdIds);
        superMapper.delete(queryWrapper);
    }

    @Override
    public void deleteByDimenIds(List<Long> dimenIds) {
        LambdaQueryWrapper<DhrQdDimensionalityTqRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DhrQdDimensionalityTqRel::getDimenId, dimenIds);
        superMapper.delete(queryWrapper);
    }

    @Override
    public ResponsePage<DhrQdDimensionalityTqRelResponse> dimensionQuestionPage(Page<DhrQdDimensionalityTqRel> page, DhrQdDimensionQuestionSearchRequest request, BaseOrder order) {
        List<DhrQdDimensionalityTqRelResponse> relResponseList = dhrQdDimensionalityTqRelMapper.dimensionQuestionPage(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, relResponseList);
    }

    @Override
    public List<DhrQdDimensionQuestionResponse> dimensionQuestionList(DhrQdDimensionQuestionSearchRequest request) {
        return dhrQdDimensionalityTqRelMapper.dimensionQuestionList(request);
    }

    @Override
    public Boolean addDimensionScoreQuestion(List<DhrQdDimensionalityTqRelRequest> addList) {
        if (CollectionUtils.isEmpty(addList)) {
            return true;
        }
        //校验维度试题
        this.checkDimenQuestion(addList);
        //获取当前维度下的试题最大序号
        Integer maxRelSort = dhrQdDimensionalityTqRelMapper.getMaxRelSort(addList.get(0).getQdId(), addList.get(0).getDimenId());
        maxRelSort = maxRelSort == null ? Integer.valueOf(0) : maxRelSort;
        List<DhrQdDimensionalityTqRel> saveList = new ArrayList<>();
        for (DhrQdDimensionalityTqRelRequest addRequest : addList) {
            DhrQdDimensionalityTqRel dhrQdDimensionalityTqRel = BeanUtil.copyProperties(addRequest, DhrQdDimensionalityTqRel.class, "id");
            dhrQdDimensionalityTqRel.setRelSort(maxRelSort + 1);
            saveList.add(dhrQdDimensionalityTqRel);
            maxRelSort++;
        }
        //保存维度试题
        return this.saveBatch(saveList);
    }

    @Override
    public boolean moveDimensionQuestion(DhrQdDimensionQuestionMoveRequest request) {
        //如果是向上移动，则中间的数据序号都需要加1
        //如果是向下移动，则中间的数据序号都需要减1
        DhrQdDimensionalityTqRel sourceDimensionalityTqRel = superMapper.selectById(request.getSourceDimenQuestionRelId());
        DhrQdDimensionalityTqRel targetDimensionalityTqRel = superMapper.selectById(request.getTargetDimenQuestionRelId());
        List<DhrQdDimensionalityTqRel> changeDimensionalityTqRelList = superMapper.selectBatchIds(request.getChangeDimenQuestionRelIds());
        if (DhrEaConstant.MOVE_UP.equals(request.getMoveType())) {
            changeDimensionalityTqRelList.forEach(dimensionalityTqRel -> dimensionalityTqRel.setRelSort(dimensionalityTqRel.getRelSort() + 1));
        } else {
            changeDimensionalityTqRelList.forEach(dimensionalityTqRel -> dimensionalityTqRel.setRelSort(dimensionalityTqRel.getRelSort() - 1));
        }
        sourceDimensionalityTqRel.setRelSort(targetDimensionalityTqRel.getRelSort());
        changeDimensionalityTqRelList.add(sourceDimensionalityTqRel);
        return this.saveOrUpdateBatch(changeDimensionalityTqRelList);
    }

    /**
     * 添加维度试题的时候校验
     * 1.不能重复添加
     * 2.同一个能力项，仅允许添加一个层级的试题
     * 3.同一个层级的试题，仅允许出现在一个考察维度下面
     *
     * @param addList
     */
    private void checkDimenQuestion(List<DhrQdDimensionalityTqRelRequest> addList) {
        Long qdId = addList.get(0).getQdId();
        //1.不能重复添加
        List<Long> tqIds = addList.stream().map(DhrQdDimensionalityTqRelRequest::getTqId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<DhrQdDimensionalityTqRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DhrQdDimensionalityTqRel::getTqId, tqIds);
        queryWrapper.eq(DhrQdDimensionalityTqRel::getQdId, qdId);
        Long existCount = superMapper.selectCount(queryWrapper);
        if (existCount > 0) {
            throw new CommRunException("dhr.talent.error.DimensionQuestionExist");
        }
        //2.同一个能力项，仅允许添加一个层级的试题
        List<DhrQdDimensionQuestionResponse> questionResponseList = this.dimensionQuestionList(new DhrQdDimensionQuestionSearchRequest(qdId, null));
        Map<Long, Long> questionMap = questionResponseList.stream().collect(Collectors.toMap(DhrQdDimensionQuestionResponse::getAbilityId,
                DhrQdDimensionQuestionResponse::getAbilityLevelId, (k1, k2) -> k1));
        Map<Long, List<DhrQdDimensionalityTqRelRequest>> addQuestionMap = addList.stream().collect(Collectors.groupingBy(DhrQdDimensionalityTqRelRequest::getAbilityId));
        addQuestionMap.forEach((abilityId, questionList) -> {
            List<Long> abilityLevelIds = questionList.stream().map(DhrQdDimensionalityTqRelRequest::getAbilityLevelId).distinct().collect(Collectors.toList());
            if (abilityLevelIds.size() > 1) {
                throw new CommRunException("dhr.talent.error.DimensionQuestionExistDifferentLevel");
            }
            Long abilityLevelId = questionMap.get(abilityId);
            if (abilityLevelId == null) {
                return;
            }
            //如果新增的试题能力项等级不属于该问卷中已经存在的能力项等级
            if (!Collections.singletonList(abilityLevelId).containsAll(abilityLevelIds)) {
                throw new CommRunException("dhr.talent.error.DimensionQuestionExistDifferentLevel");
            }
        });
        //3.同一个层级的试题，仅允许出现在一个考察维度下面
        addList.forEach(request -> {
            //其他维度相同能力项等级的试题数量
            long count = questionResponseList.stream().filter(questionResponse -> !request.getDimenId().equals(questionResponse.getDimenId())
                    && request.getAbilityLevelId().equals(questionResponse.getAbilityLevelId())).count();
            if (count > 0) {
                throw new CommRunException("dhr.talent.error.DimensionQuestionExistSameLevelOnDifferentDimen");
            }
        });
    }
}

