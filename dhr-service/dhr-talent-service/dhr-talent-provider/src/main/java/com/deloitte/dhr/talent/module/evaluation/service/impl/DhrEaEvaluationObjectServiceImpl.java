package com.deloitte.dhr.talent.module.evaluation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.bpm.constant.WebConstant;
import com.deloitte.bpm.provider.ProcessInstancesProvider;
import com.deloitte.bpm.vo.ResultVO;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.Request;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.LanguageUtil;
import com.deloitte.dhr.common.base.utils.MessageUtils;
import com.deloitte.dhr.excel.parser.handler.I18nCellWriteHandler;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheet;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheetData;
import com.deloitte.dhr.excel.parser.template.ExcelParserTemplate;
import com.deloitte.dhr.excel.parser.util.MessageUtil;
import com.deloitte.dhr.mda.module.base.EmployeeFromPersonnelWarehouseFeignInterface;
import com.deloitte.dhr.mda.module.base.pojo.EmployeeFromPersonnelWarehouseRequest;
import com.deloitte.dhr.mda.module.base.pojo.EmployeeFromPersonnelWarehouseResponse;
import com.deloitte.dhr.talent.module.evaluation.constant.DhrEaConstant;
import com.deloitte.dhr.talent.module.evaluation.domain.*;
import com.deloitte.dhr.talent.module.evaluation.excel.dto.*;
import com.deloitte.dhr.talent.module.evaluation.excel.listener.DhrEaEvaluationObjectListener;
import com.deloitte.dhr.talent.module.evaluation.excel.vo.DhrEaEvaluationObjectVO;
import com.deloitte.dhr.talent.module.evaluation.mapper.*;
import com.deloitte.dhr.talent.module.evaluation.pojo.*;
import com.deloitte.dhr.talent.module.evaluation.service.*;
import com.deloitte.dhr.talent.util.DynamicExcelUtil;
import com.deloitte.dhr.talent.util.RequestContextUtil;
import com.deloitte.dhr.talent.util.RequestHeaderHandler;
import com.deloitte.dhr.talent.util.TalentFeignMethodUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Comment：dhr_ea_evaluation_object360测评 - 测评活动管理 - 测评对象表
 *
 * <AUTHOR>
 * @date 2022-09-15
 */
@Service
@Slf4j
public class DhrEaEvaluationObjectServiceImpl extends SuperServiceImpl<DhrEaEvaluationObjectMapper, DhrEaEvaluationObject> implements DhrEaEvaluationObjectService {

    @Autowired
    private DhrEaEvaluationObjectMapper dhrEaEvaluationObjectMapper;
    @Autowired
    private ExcelParserTemplate excelParserTemplate;
    @Autowired
    private EmployeeFromPersonnelWarehouseFeignInterface employeeFromPersonnelWarehouseFeignInterface;
    @Autowired
    private DhrEaActivityInfoMapper dhrEaActivityInfoMapper;
    @Autowired
    private ProcessInstancesProvider processInstancesProvider;
    @Autowired
    private DhrEaEvaluationWeightMapper dhrEaEvaluationWeightMapper;
    @Autowired
    private DhrEaObjReviewerRelService dhrEaObjReviewerRelService;
    @Autowired
    private DhrEaEvaluationReviewerMapper dhrEaEvaluationReviewerMapper;
    @Autowired
    private DhrEaEvaluationStatisticsService dhrEaEvaluationStatisticsService;
    @Autowired
    private DhrEaEvaluatorAsyncService dhrEaEvaluatorAsyncService;
    @Autowired
    private DhrEaEvaluationStatisticsMapper dhrEaEvaluationStatisticsMapper;
    @Autowired
    private TalentFeignMethodUtil talentFeignMethodUtil;
    @Autowired
    private DhrEaReportAbilityScoreService dhrEaReportAbilityScoreService;
    @Autowired
    private DhrEaReportAbilityScoreMapper dhrEaReportAbilityScoreMapper;
    @Autowired
    private DhrEaReportDimenScoreService dhrEaReportDimenScoreService;
    @Autowired
    private DhrEaReportQuestionScoreService dhrEaReportQuestionScoreService;
    @Autowired
    private DhrEaReportQuestionScoreMapper dhrEaReportQuestionScoreMapper;
    @Autowired
    private DhrEaReportTotalScoreService dhrEaReportTotalScoreService;
    @Autowired
    private DhrEaEvaluationPathService dhrEaEvaluationPathService;
    @Autowired
    private DhrEaReportPersonalHistoryService dhrEaReportPersonalHistoryService;

    /**
     * 异步执行线程池
     */
    private final ExecutorService cachedThreadPool = new ThreadPoolExecutor(10, 50, 300, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    private static final String EA_ID = "eaId";

    @Override
    public List<DhrEaEvaluationObjectResponse> getListByEaId(Long eaId) {
        LambdaQueryWrapper<DhrEaEvaluationObject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        wrapper.orderByAsc(DhrEaEvaluationObject::getEaSort);
        List<DhrEaEvaluationObject> dhrEaEvaluationObjects = dhrEaEvaluationObjectMapper.selectList(wrapper);
        return BeanUtil.copyToList(dhrEaEvaluationObjects, DhrEaEvaluationObjectResponse.class);
    }

    @Override
    public List<DhrEaEvaluationObjectResponse> getListByCodes(Long eaId, List<String> codes) {
        LambdaQueryWrapper<DhrEaEvaluationObject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        wrapper.in(DhrEaEvaluationObject::getEmpCode, codes);
        List<DhrEaEvaluationObject> dhrEaEvaluationObjects = dhrEaEvaluationObjectMapper.selectList(wrapper);
        return BeanUtil.copyToList(dhrEaEvaluationObjects, DhrEaEvaluationObjectResponse.class);
    }

    @Override
    public List<DhrEaEvaluationObjectResponse> getListByRevCode(Long eaId, String revCode) {
        return dhrEaEvaluationObjectMapper.getListByRevCode(eaId, revCode);
    }

    @Override
    public ResponsePage inviteReviewerProgress(Page page, DhrEaEvaluationObjectRequest request) {
        List<DhrEaEvaluationObjectResponse> list = dhrEaEvaluationObjectMapper.inviteReviewerProgress(page, request);
        return new ResponsePage(page, list);
    }

    @Override
    public ResponsePage questionProgress(Long current, Long size, Request<DhrEaEvaluationObjectRequest> dhrEaEvaluationObjectRequestRequest) {
        Page page = new Page<>(current, size);
        String orderStr = null;
        if (dhrEaEvaluationObjectRequestRequest.getOrder() != null
                && dhrEaEvaluationObjectRequestRequest.getOrder().getField() != null) {
            orderStr = this.adjustOrder(dhrEaEvaluationObjectRequestRequest.getOrder());
        }
        buildCondition(dhrEaEvaluationObjectRequestRequest.get());
        List list = dhrEaEvaluationObjectMapper.questionProgress(page, dhrEaEvaluationObjectRequestRequest.get(), orderStr);
        return new ResponsePage(page, list);
    }

    private void buildCondition(DhrEaEvaluationObjectRequest request) {
        request.setGteSJFinishRate(request.getGteSJFinishRate() == null ? null : request.getGteSJFinishRate().divide(BigDecimal.valueOf(100)));
        request.setLteSJFinishRate(request.getLteSJFinishRate() == null ? null : request.getLteSJFinishRate().divide(BigDecimal.valueOf(100)));
        request.setGtePJFinishRate(request.getGtePJFinishRate() == null ? null : request.getGtePJFinishRate().divide(BigDecimal.valueOf(100)));
        request.setLtePJFinishRate(request.getLtePJFinishRate() == null ? null : request.getLtePJFinishRate().divide(BigDecimal.valueOf(100)));
        request.setGteXJFinishRate(request.getGteXJFinishRate() == null ? null : request.getGteXJFinishRate().divide(BigDecimal.valueOf(100)));
        request.setLteXJFinishRate(request.getLteXJFinishRate() == null ? null : request.getLteXJFinishRate().divide(BigDecimal.valueOf(100)));
        request.setGteQTFinishRate(request.getGteQTFinishRate() == null ? null : request.getGteQTFinishRate().divide(BigDecimal.valueOf(100)));
        request.setLteQTFinishRate(request.getLteQTFinishRate() == null ? null : request.getLteQTFinishRate().divide(BigDecimal.valueOf(100)));
    }

    @Override
    public boolean exportInviteReviewer(Request<DhrEaEvaluationObjectRequest> dhrEaEvaluationObjectRequest) {
        DhrEaEvaluationObjectRequest request = dhrEaEvaluationObjectRequest.get();
        DhrEaActivityInfo activityInfo = dhrEaActivityInfoMapper.selectById(request.getEaId());
        ResponsePage<DhrEaEvaluationObjectResponse> responsePage = this.inviteReviewerProgress(new Page(1, -1), request);
        List<DhrEaEvaluationObjectResponse> dataList = responsePage.getDataList();
        List<InviteReviewerDTO> inviteReviewerDTOS = dataList.stream().map(data -> {
            InviteReviewerDTO inviteReviewerDTO = BeanUtil.toBean(data, InviteReviewerDTO.class);
            inviteReviewerDTO.setUrgeTime(DateUtil.formatDateTime(data.getUrgeTime()));
            return inviteReviewerDTO;
        }).collect(Collectors.toList());
        ExcelSheetData excelSheetData = new ExcelSheetData<>(InviteReviewerDTO.class, inviteReviewerDTOS);
        String fileName = activityInfo.getEaName() + "-" + MessageUtil.getText("export.name.dhr.evaluation.inviteReviewer") + "-" + DateUtil.now() + ".xlsx";
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(fileName, CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), null);
        return false;
    }

    @Override
    public boolean exportObjectProgress(Request<DhrEaEvaluationObjectRequest> dhrEaEvaluationObjectRequest) {
        //固定表头
        List<List<String>> headerList = DynamicExcelUtil.headerList(ObjProgressDTO.class);
        //忽略表头
        List<String> ignoreHeader = Lists.newArrayList("id", "eaId");
        //动态表头
        LambdaQueryWrapper<DhrEaEvaluationWeight> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DhrEaEvaluationWeight::getEaId, dhrEaEvaluationObjectRequest.getParam().getEaId());
        List<DhrEaEvaluationWeight> dhrEaEvaluationWeights = dhrEaEvaluationWeightMapper.selectList(wrapper);
        dhrEaEvaluationWeights.forEach(weight -> {
            ignoreHeader.addAll(ignoreHeader(weight));
            if ("BR".equals(weight.getEvaluationType())) {
                return;
            }
            headerList.addAll(buildHeader(weight));
        });
        //对应前端列表字段顺序的改动
        headerList.addAll(dhrEaEvaluationWeights.stream()
                .filter(w -> "BR".equals(w.getEvaluationType()) && DhrEaConstant.YES.equals(w.getEnabled()))
                .map(w -> Lists.newArrayList(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.oneselfProgress}")))
                .collect(Collectors.toList()));
        //总体统计在最后
        headerList.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.totalStatistics}"));
        //忽略表头转数组
        String[] ignoreHeaderArray = new String[ignoreHeader.size()];
        ignoreHeader.toArray(ignoreHeaderArray);
        DhrEaEvaluationObjectRequest request = dhrEaEvaluationObjectRequest.get();
        DhrEaActivityInfo activityInfo = dhrEaActivityInfoMapper.selectById(request.getEaId());
        ResponsePage<DhrEaEvaluationObjectResponse> responsePage = this.questionProgress(1L, -1L, dhrEaEvaluationObjectRequest);
        List<DhrEaEvaluationObjectResponse> dataList = responsePage.getDataList();
        List<ObjProgressfixedDTO> objProgressfixedDTOS = BeanUtil.copyToList(dataList, ObjProgressfixedDTO.class);
        List<LinkedHashMap<String, String>> mapList = Lists.newArrayList();
        objProgressfixedDTOS.forEach(r -> {
            LinkedHashMap<String, String> map = Maps.newLinkedHashMap();
            BeanUtil.copyProperties(r, map, ignoreHeaderArray);
            mapList.add(map);
        });
        List<List<String>> excelList = mapList.stream().map(m -> m.values().stream().collect(Collectors.toList())).collect(Collectors.toList());
        ExcelSheetData excelSheetData = new ExcelSheetData<>(null, excelList);
        String fileName = activityInfo.getEaName() + "-" + MessageUtil.getText("export.name.dhr.evaluation.objProgress") + "-" + DateUtil.now() + ".xlsx";
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(fileName, CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), headerList);
        return false;
    }

    /**
     * 动态表头
     *
     * @param weight
     * @return
     */
    private List<List<String>> buildHeader(DhrEaEvaluationWeight weight) {
        List<List<String>> header = Lists.newArrayList();
        if (DhrEaConstant.NO.equals(weight.getEnabled())) {
            return header;
        }
        switch (weight.getEvaluationType()) {
            case "SJ":
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.highrLevelProgress}"));
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.higherFinishRate}"));
                break;
            case "PJ":
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.sameLevelProgress}"));
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.sameFinishRate}"));
                break;
            case "XJ":
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.lowerLevelProgress}"));
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.lowerFinishRate}"));
                break;
            case "QT":
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.otherProgress}"));
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.otherFinishRate}"));
                break;
            case "BR":
                header.add(Lists.newArrayList("${export.header.dhr.evaluation.objProgress.oneselfProgress}"));
                break;
            default:
                break;
        }
        return header;
    }

    /**
     * 忽略的表头
     *
     * @param weight
     * @return
     */
    private List<String> ignoreHeader(DhrEaEvaluationWeight weight) {
        List<String> ignoreHeader = Lists.newArrayList();
        if (DhrEaConstant.YES.equals(weight.getEnabled())) {
            return ignoreHeader;
        }
        switch (weight.getEvaluationType()) {
            case "SJ":
                ignoreHeader.add("highrLevelProgress");
                ignoreHeader.add("higherFinishRate");
                break;
            case "PJ":
                ignoreHeader.add("sameLevelProgress");
                ignoreHeader.add("sameFinishRate");
                break;
            case "XJ":
                ignoreHeader.add("lowerLevelProgress");
                ignoreHeader.add("lowerFinishRate");
                break;
            case "QT":
                ignoreHeader.add("otherProgress");
                ignoreHeader.add("otherFinishRate");
                break;
            case "BR":
                ignoreHeader.add("oneselfProgress");
                break;
            default:
                break;
        }
        return ignoreHeader;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DhrEaEvaluationObjectResponse validAndAddObjs(Request<EmployeeFromPersonnelWarehouseRequest> request, Long eaId) {
        //在职
        request.getParam().setEngageStatus(3);
        List<EmployeeFromPersonnelWarehouseResponse> employees = employeeFromPersonnelWarehouseFeignInterface.listEmp(null, 1L, -1L, request).getData().getDataList();
        DhrEaEvaluationObjectResponse response = new DhrEaEvaluationObjectResponse();
        if (CollUtil.isEmpty(employees)) {
            response.setValidType(DhrEaConstant.SUCCESS_TYPE);
            return response;
        }
        //拟添加的评价对象已存在，继续添加则跳过存在校验
        if (!DhrEaConstant.EXISTS_TYPE.equals(request.getParam().getValidType())) {
            //校验评价对象已存在
            response = validExists(employees, eaId);
            if (response.getValidType() != null) {
                return response;
            }
        }
        List<String> empCodes = employees.stream().map(e -> e.getEmpCode()).collect(Collectors.toList());
        LambdaQueryWrapper<DhrEaEvaluationObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        queryWrapper.in(DhrEaEvaluationObject::getEmpCode, empCodes);
        //已存在的对象列表
        List<DhrEaEvaluationObject> oldObjs = list(queryWrapper);
        List<String> oldObjCodes = oldObjs.stream().map(o -> o.getEmpCode()).collect(Collectors.toList());
        //删除列表
        List<String> delList = employees.stream().filter(o -> oldObjCodes.contains(o.getEmpCode())).map(o -> o.getEmpCode()).collect(Collectors.toList());
        //删除已存在的测评对象
        delObjs(eaId, delList);
        //新增测评对象
        addObjs(employees, eaId);
        response.setValidType(DhrEaConstant.SUCCESS_TYPE);
        response.setSuccessList(Lists.newArrayList());
        return response;
    }

    @Override
    public void del(List<Long> ids) {
        List<DhrEaEvaluationObject> objects = listByIds(ids);
        if (CollUtil.isEmpty(objects)) {
            return;
        }
        DhrEaEvaluationObject object = objects.get(0);
        LambdaQueryWrapper<DhrEaObjReviewerRel> wrapper = new LambdaQueryWrapper<DhrEaObjReviewerRel>()
                .eq(DhrEaObjReviewerRel::getEaId, object.getEaId())
                .eq(DhrEaObjReviewerRel::getEvaluationObjectCode, object.getEmpCode())
                .eq(DhrEaObjReviewerRel::getIsSendEa, DhrEaConstant.YES);
        if (CollUtil.isNotEmpty(dhrEaObjReviewerRelService.list(wrapper))) {
            throw new CommRunException("dhr.talent.error.objNotAllowDel");
        }
        delObjs(objects.get(0).getEaId(), objects.stream().map(o -> o.getEmpCode()).collect(Collectors.toList()));
    }

    /**
     * 删除测评对象及关联表
     *
     * @param eaId
     * @param objCodes
     * @return void
     * <AUTHOR>
     * @Date 17:10 22/09/2022
     */
    private void delObjs(Long eaId, List<String> objCodes) {
        if (CollUtil.isEmpty(objCodes)) {
            return;
        }
        //删除测评类型统计
        LambdaQueryWrapper<DhrEaEvaluationStatistics> staWrapper = new LambdaQueryWrapper<>();
        staWrapper.eq(DhrEaEvaluationStatistics::getEaId, eaId);
        staWrapper.in(DhrEaEvaluationStatistics::getEvaluationObject, objCodes);
        dhrEaEvaluationStatisticsService.remove(staWrapper);

        //删除测评关系
        LambdaQueryWrapper<DhrEaObjReviewerRel> relWrapper = new LambdaQueryWrapper<>();
        relWrapper.eq(DhrEaObjReviewerRel::getEaId, eaId);
        relWrapper.in(DhrEaObjReviewerRel::getEvaluationObjectCode, objCodes);
        List<DhrEaObjReviewerRel> rels = dhrEaObjReviewerRelService.list(relWrapper);
        dhrEaObjReviewerRelService.remove(relWrapper);

        //删除评价者
        if (CollUtil.isNotEmpty(rels)) {
            List<String> revCodes = rels.stream().map(r -> r.getEvaluationReviewerCode()).distinct().collect(Collectors.toList());
            relWrapper.clear();
            relWrapper.eq(DhrEaObjReviewerRel::getEaId, eaId);
            relWrapper.in(DhrEaObjReviewerRel::getEvaluationReviewerCode, revCodes);
            //排除掉关联不为空的评价者
            revCodes.removeAll(dhrEaObjReviewerRelService.list(relWrapper).stream()
                    .map(r -> r.getEvaluationReviewerCode()).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(revCodes)) {
                dhrEaEvaluationReviewerMapper.delete(new LambdaQueryWrapper<DhrEaEvaluationReviewer>()
                        .eq(DhrEaEvaluationReviewer::getEaId, eaId)
                        .in(DhrEaEvaluationReviewer::getEmpCode, revCodes));
            }
        }

        //删除测评对象
        LambdaQueryWrapper<DhrEaEvaluationObject> objWrapper = new LambdaQueryWrapper<>();
        objWrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        objWrapper.in(DhrEaEvaluationObject::getEmpCode, objCodes);
        this.remove(objWrapper);

        //更新测评者的测评人统计数量
        dhrEaEvaluatorAsyncService.updateReviewerNum(eaId);
    }

    @Override
    public void addObjs(List<EmployeeFromPersonnelWarehouseResponse> employees, Long eaId) {
        LambdaQueryWrapper<DhrEaEvaluationObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        queryWrapper.orderByDesc(DhrEaEvaluationObject::getEaSort);
        List<DhrEaEvaluationObject> objs = dhrEaEvaluationObjectMapper.selectList(queryWrapper);
        //排序
        AtomicInteger esSort = new AtomicInteger(CollUtil.isNotEmpty(objs) ? objs.get(0).getEaSort() + 1 : 1);
        List<DhrEaEvaluationObject> dhrEaEvaluationObjects = employees.stream().map(rep -> buildObj(eaId, rep, esSort)).collect(Collectors.toList());
        this.saveBatch(dhrEaEvaluationObjects);
        //初始化测评类别统计
        List<DhrEaEvaluationStatistics> statistics = Lists.newArrayList();
        employees.forEach(employee ->
                DhrEaConstant.EvaluationType.LIST.forEach(type -> {
                    DhrEaEvaluationStatistics statistic = new DhrEaEvaluationStatistics();
                    statistic.setEaId(eaId);
                    statistic.setEvaluationObject(employee.getEmpCode());
                    statistic.setEvaluationType(type);
                    statistic.setEvaluationFinished(0);
                    statistic.setEvaluationUnfinished(0);
                    statistic.setEvaluationCount(0);
                    statistic.setFinishRate(BigDecimal.ZERO);
                    statistic.setEnabled(DhrEaConstant.YES);
                    statistics.add(statistic);
                })
        );
        dhrEaEvaluationStatisticsService.saveBatch(statistics);
    }


    /**
     * 校验评价对象已存在
     *
     * @param employees
     * @param eaId
     * @return com.deloitte.dhr.talent.module.evaluation.pojo.DhrEaEvaluationObjectResponse
     * <AUTHOR>
     * @Date 14:00 21/09/2022
     */
    private DhrEaEvaluationObjectResponse validExists(List<EmployeeFromPersonnelWarehouseResponse> employees, Long eaId) {
        DhrEaEvaluationObjectResponse dhrEaEvaluationObjectResponse = new DhrEaEvaluationObjectResponse();
        List<EmployeeFromPersonnelWarehouseResponse> errorList = Lists.newArrayList();
        List<EmployeeFromPersonnelWarehouseResponse> successList = Lists.newArrayList();
        LambdaQueryWrapper<DhrEaEvaluationObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        List<DhrEaEvaluationObject> objs = dhrEaEvaluationObjectMapper.selectList(queryWrapper);
        List<String> objCodes = objs.stream().map(o -> o.getEmpCode()).collect(Collectors.toList());
        List<String> sendObjCodes;
        if (CollUtil.isEmpty(objCodes)) {
            sendObjCodes = Lists.newArrayList();
        } else {
            LambdaQueryWrapper<DhrEaObjReviewerRel> wrapper = new LambdaQueryWrapper<DhrEaObjReviewerRel>()
                    .eq(DhrEaObjReviewerRel::getEaId, eaId)
                    .in(DhrEaObjReviewerRel::getEvaluationObjectCode, objCodes);
            List<DhrEaObjReviewerRel> dhrEaObjReviewerRels = dhrEaObjReviewerRelService.list(wrapper);
            sendObjCodes = dhrEaObjReviewerRels.stream().filter(r -> DhrEaConstant.YES.equals(r.getIsSendEa()))
                    .map(r -> r.getEvaluationObjectCode()).collect(Collectors.toList());
        }

        employees.forEach(employee -> {
            //该活动id下是否存在该测评对象
            if (objCodes.contains(employee.getEmpCode())) {
                //发送了测评的对象给个禁止勾选标识
                if (sendObjCodes.contains(employee.getEmpCode())) {
                    employee.setIsSendEa(DhrEaConstant.YES);
                } else {
                    employee.setIsSendEa(DhrEaConstant.NO);
                }
                errorList.add(employee);
            } else {
                successList.add(employee);
            }
        });
        dhrEaEvaluationObjectResponse.setErrorList(errorList);
        dhrEaEvaluationObjectResponse.setSuccessList(successList);
        if (CollUtil.isNotEmpty(errorList)) {
            dhrEaEvaluationObjectResponse.setValidType(DhrEaConstant.EXISTS_TYPE);
        }
        return dhrEaEvaluationObjectResponse;
    }


    @Override
    public boolean delObjByEaId(Long eaId) {
        //删除测评类型统计
        LambdaQueryWrapper<DhrEaEvaluationStatistics> staWrapper = new LambdaQueryWrapper<>();
        staWrapper.eq(DhrEaEvaluationStatistics::getEaId, eaId);
        dhrEaEvaluationStatisticsService.remove(staWrapper);

        //删除测评关系
        LambdaQueryWrapper<DhrEaObjReviewerRel> relWrapper = new LambdaQueryWrapper<>();
        relWrapper.eq(DhrEaObjReviewerRel::getEaId, eaId);
        List<DhrEaObjReviewerRel> rels = dhrEaObjReviewerRelService.list(relWrapper);
        dhrEaObjReviewerRelService.remove(relWrapper);

        //删除评价者
        if (CollUtil.isNotEmpty(rels)) {
            List<String> revCodes = rels.stream().map(r -> r.getEvaluationReviewerCode()).distinct().collect(Collectors.toList());
            relWrapper.clear();
            relWrapper.eq(DhrEaObjReviewerRel::getEaId, eaId);
            relWrapper.in(DhrEaObjReviewerRel::getEvaluationReviewerCode, revCodes);
            //排除掉关联不为空的评价者
            revCodes.removeAll(dhrEaObjReviewerRelService.list(relWrapper).stream()
                    .map(r -> r.getEvaluationReviewerCode()).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(revCodes)) {
                dhrEaEvaluationReviewerMapper.delete(new LambdaQueryWrapper<DhrEaEvaluationReviewer>()
                        .eq(DhrEaEvaluationReviewer::getEaId, eaId)
                        .in(DhrEaEvaluationReviewer::getEmpCode, revCodes));
            }
        }

        //删除测评对象
        LambdaQueryWrapper<DhrEaEvaluationObject> objWrapper = new LambdaQueryWrapper<>();
        objWrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        this.remove(objWrapper);

        //更新测评者的测评人统计数量
        dhrEaEvaluatorAsyncService.updateReviewerNum(eaId);
        return true;
    }

    private DhrEaEvaluationObject buildObj(Long eaId, EmployeeFromPersonnelWarehouseResponse empInfo, AtomicInteger esSort) {
        DhrEaEvaluationObject dhrEaEvaluationObject = new DhrEaEvaluationObject();
        dhrEaEvaluationObject.setEaId(eaId);
        dhrEaEvaluationObject.setOaCode(empInfo.getEmpCode());
        dhrEaEvaluationObject.setEmpCode(empInfo.getEmpCode());
        dhrEaEvaluationObject.setEmpName(empInfo.getFullname());
        dhrEaEvaluationObject.setEmpOrgCode(empInfo.getOrgCode());
        dhrEaEvaluationObject.setEmpOrgFullPath(empInfo.getOrgPathName());
        dhrEaEvaluationObject.setEmpOrgCodeFullPath(empInfo.getOrgPath());
        dhrEaEvaluationObject.setEmpSequenceCode(empInfo.getSeqCode());
        dhrEaEvaluationObject.setEmpSequenceName(empInfo.getSeqName());
        dhrEaEvaluationObject.setEmpPositionCode(empInfo.getPositionCode());
        dhrEaEvaluationObject.setEmpPositionName(empInfo.getPositionDesc());
        dhrEaEvaluationObject.setEmpStatus(empInfo.getEngageStatus());
        dhrEaEvaluationObject.setHireDate(StrUtil.isNotBlank(empInfo.getHireDate())
                ? DateUtil.parseDate(empInfo.getHireDate()) : null);
        dhrEaEvaluationObject.setEmpEvaluationReviewerCount(0);
        dhrEaEvaluationObject.setIsUrge(DhrEaConstant.NO);
        dhrEaEvaluationObject.setEaSort(esSort.getAndAdd(1));
        dhrEaEvaluationObject.setQuestionnaireStatus(DhrEaConstant.ObjectQuestionnaireStatus.NORMAL);

        return dhrEaEvaluationObject;
    }

    @Override
    public boolean setTop(Long id) {
        DhrEaEvaluationObject object = getById(id);
        if (object == null) {
            return false;
        }
        LambdaUpdateWrapper<DhrEaEvaluationObject> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DhrEaEvaluationObject::getEaId, object.getEaId());
        wrapper.orderByAsc(DhrEaEvaluationObject::getEaSort);
        List<DhrEaEvaluationObject> objects = list(wrapper);
        object.setEaSort(CollUtil.isEmpty(objects) ? 0 : objects.get(0).getEaSort() - 1);
        update(object);
        return true;
    }

    @Override
    public boolean importData(MultipartFile file, String bsCode, Long eaId, Boolean isCover) {
        try {
            excelParserTemplate.importExcelAsync(bsCode, file.getInputStream(), file.getOriginalFilename()
                    , "sheet", DhrEaEvaluationObjectVO.class, new DhrEaEvaluationObjectListener(
                            employeeFromPersonnelWarehouseFeignInterface, this, isCover, eaId
                            , new Locale(LanguageUtil.getLanguage()), SecurityContextHolder.getContext()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return false;
    }

    @Override
    public ResponsePage objList(Long current, Long size, DhrEaEvaluationObjectRequest request) {
        Page page = new Page<>(current, size);
        List<DhrEaEvaluationObjectResponse> objs = dhrEaEvaluationObjectMapper.objList(page, request);
        //测评者数量
        List<DhrEaEvaluationReviewerResponse> reviewers = dhrEaEvaluationReviewerMapper.revListByObjId(request.getEaId(), null, null);
        Map<String, Long> countMap = reviewers.stream().collect(Collectors.groupingBy(r -> r.getObjCode(), Collectors.counting()));
        //是否发送测评
        LambdaQueryWrapper<DhrEaObjReviewerRel> wrapper = new LambdaQueryWrapper<DhrEaObjReviewerRel>()
                .eq(DhrEaObjReviewerRel::getEaId, request.getEaId());
        List<DhrEaObjReviewerRel> dhrEaObjReviewerRels = dhrEaObjReviewerRelService.list(wrapper);
        List<String> sendObjCodes = dhrEaObjReviewerRels.stream().filter(r -> DhrEaConstant.YES.equals(r.getIsSendEa()))
                .map(r -> r.getEvaluationObjectCode()).collect(Collectors.toList());
        objs.forEach(obj -> {
            obj.setReviewerNum(countMap.get(obj.getEmpCode()) == null ? 0 : countMap.get(obj.getEmpCode()).intValue());
            if (sendObjCodes.contains(obj.getEmpCode())) {
                obj.setIsSendEa(DhrEaConstant.YES);
            } else {
                obj.setIsSendEa(DhrEaConstant.NO);
            }
        });
        return new ResponsePage(page, objs);
    }

    @Override
    public ResponsePage objTeamReportList(Long current, Long size, DhrEaEvaluationObjectRequest request) {
        Page page = new Page<>(current, size);
        List<DhrEaEvaluationObjectResponse> objs = dhrEaEvaluationObjectMapper.objTeamReportList(page, request);
        return new ResponsePage(page, objs);
    }

    @Override
    public boolean export(Long eaId) {
        List<DhrEaEvaluationObjectResponse> dataList = this.getListByEaId(eaId);
        List<ObjDTO> objDTOS = BeanUtil.copyToList(dataList, ObjDTO.class);
        ExcelSheetData excelSheetData = new ExcelSheetData<>(ObjDTO.class, objDTOS);
        String fileName = MessageUtil.getText("export.name.dhr.evaluation.object") + "-" + DateUtil.now() + ".xlsx";
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(fileName, CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), null);
        return false;
    }

    @Override
    public boolean excelTemp() {
        ExcelSheetData excelSheetData = new ExcelSheetData<>(DhrEaEvaluationObjectVO.class, Lists.newArrayList(new DhrEaEvaluationObjectVO()));
        String fileName = MessageUtil.getText("temp.name.dhr.evaluation.object") + "-" + DateUtil.now() + ".xlsx";
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(fileName, CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), null);
        return false;
    }

    @Override
    public ResponsePage<DhrEaEvaluationObjectResponse> objectPageList(Page<Object> page, DhrEaAnswerManageSearchRequest request, BaseOrder order) {
        //查询主数据组织路径信息
        if (StringUtils.isNotBlank(request.getOrgCode())) {
            request.setOrgPath(talentFeignMethodUtil.findByOrgCode(request.getOrgCode()).getOrgPath());
        }
        List<DhrEaEvaluationObjectResponse> responseList = dhrEaEvaluationObjectMapper.objectPageList(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, responseList);
    }

    @Override
    public Boolean updateCurrentStage(String processInstanceId, String currentStage) {
        LambdaUpdateWrapper<DhrEaEvaluationObject> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DhrEaEvaluationObject::getProcessInstanceId, processInstanceId);
        DhrEaEvaluationObject evaluationObject = new DhrEaEvaluationObject();
        evaluationObject.setCurrentStage(currentStage);
        int rows = dhrEaEvaluationObjectMapper.update(evaluationObject, wrapper);
        return rows > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean closeQuestionnaire(Long id) {
        DhrEaEvaluationObject dhrEaEvaluationObject = dhrEaEvaluationObjectMapper.selectById(id);
        if (dhrEaEvaluationObject == null) {
            throw new CommRunException("dhr.mdb.db.error.notFound");
        }
        //验证测评活动是否停用，停用后才可以关闭问卷
        DhrEaActivityInfo dhrEaActivityInfo = dhrEaActivityInfoMapper.selectById(dhrEaEvaluationObject.getEaId());
        if (dhrEaActivityInfo == null) {
            throw new CommRunException("dhr.mdb.db.error.notFound");
        }
        if (!DhrEaConstant.ActivityInfo.EA_STATUS_TY.equals(dhrEaActivityInfo.getEaStatus())) {
            throw new CommRunException("dhr.talent.error.notAllowedCloseNoStop");
        }
        Long eaId = dhrEaEvaluationObject.getEaId();
        String empCode = dhrEaEvaluationObject.getEmpCode();
        //删除该考核对象分数记录
        LambdaQueryWrapper<DhrEaReportAbilityScore> abilityScoreQueryWrapper = new LambdaQueryWrapper<>();
        abilityScoreQueryWrapper.eq(DhrEaReportAbilityScore::getEaId, eaId);
        abilityScoreQueryWrapper.eq(DhrEaReportAbilityScore::getEmpCode, empCode);
        dhrEaReportAbilityScoreService.remove(abilityScoreQueryWrapper);
        LambdaQueryWrapper<DhrEaReportDimenScore> dimenScoreQueryWrapper = new LambdaQueryWrapper<>();
        dimenScoreQueryWrapper.eq(DhrEaReportDimenScore::getEaId, eaId);
        dimenScoreQueryWrapper.eq(DhrEaReportDimenScore::getEmpCode, empCode);
        dhrEaReportDimenScoreService.remove(dimenScoreQueryWrapper);
        LambdaQueryWrapper<DhrEaReportQuestionScore> questionScoreQueryWrapper = new LambdaQueryWrapper<>();
        questionScoreQueryWrapper.eq(DhrEaReportQuestionScore::getEaId, eaId);
        questionScoreQueryWrapper.eq(DhrEaReportQuestionScore::getEmpCode, empCode);
        dhrEaReportQuestionScoreService.remove(questionScoreQueryWrapper);
        LambdaQueryWrapper<DhrEaReportTotalScore> totalScoreQueryWrapper = new LambdaQueryWrapper<>();
        totalScoreQueryWrapper.eq(DhrEaReportTotalScore::getEaId, eaId);
        totalScoreQueryWrapper.eq(DhrEaReportTotalScore::getEmpCode, empCode);
        dhrEaReportTotalScoreService.remove(totalScoreQueryWrapper);
        LambdaQueryWrapper<DhrEaReportPersonalHistory> personalHistoryQueryWrapper = new LambdaQueryWrapper<>();
        personalHistoryQueryWrapper.eq(DhrEaReportPersonalHistory::getEaId, eaId);
        personalHistoryQueryWrapper.eq(DhrEaReportPersonalHistory::getEmpCode, empCode);
        dhrEaReportPersonalHistoryService.remove(personalHistoryQueryWrapper);
        //更新该考核对象状态为关闭问卷状态
        dhrEaEvaluationObject.setQuestionnaireStatus(DhrEaConstant.ObjectQuestionnaireStatus.CLOSE);
        return this.saveOrUpdate(dhrEaEvaluationObject);
    }

    @Override
    public void updateEvaluatorNumByLock(Long eaId, String empCode, Integer count) {
        if (count == null) {
            return;
        }
        LambdaUpdateWrapper<DhrEaEvaluationObject> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        wrapper.eq(DhrEaEvaluationObject::getEmpCode, empCode);
        DhrEaEvaluationObject obj = getOne(wrapper);
        if (obj == null) {
            return;
        }
        wrapper.eq(DhrEaEvaluationObject::getEmpEvaluationReviewerCount, obj.getEmpEvaluationReviewerCount());
        wrapper.set(DhrEaEvaluationObject::getEmpEvaluationReviewerCount, Math.addExact(obj.getEmpEvaluationReviewerCount(), count));
        if (update(wrapper)) {
            return;
        }
        updateEvaluatorNumByLock(eaId, empCode, count);
    }

    @Override
    public String endSelfSelect(Long eaId) {
        //查询流程还没走完的测评对象
        LambdaQueryWrapper<DhrEaEvaluationObject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        queryWrapper.eq(DhrEaEvaluationObject::getProcessStatus, DhrEaConstant.ApprovalStatus.SUCCESS);
        queryWrapper.in(DhrEaEvaluationObject::getCurrentStage, Arrays.asList(DhrEaConstant.ApprovalCurrentStage.SELF_SELECTED,
                DhrEaConstant.ApprovalCurrentStage.SUPERIOR_APPROVAL, DhrEaConstant.ApprovalCurrentStage.HR_REVIEW));
        List<DhrEaEvaluationObject> dhrEaEvaluationObjects = dhrEaEvaluationObjectMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(dhrEaEvaluationObjects)) {
            return MessageUtils.toLocale("dhr.talent.evaluation.selfSelectAllOver", "");
        }
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        SecurityContext context = SecurityContextHolder.getContext();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        dhrEaEvaluationObjects.forEach(dhrEaEvaluationObject ->
                cachedThreadPool.execute(() -> {
                    RequestHeaderHandler.setHeaderMap(headerMap);
                    // 保证feign调用正常
                    SecurityContextHolder.setContext(context);
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    ResultVO<String> resultVO = processInstancesProvider.deleteProcessInstance("system", dhrEaEvaluationObject.getProcessInstanceId(), null);
                    log.info("调用流程引擎删除流程接口，入参为：{},返回参数为：{}", dhrEaEvaluationObject.getProcessInstanceId(), JSONObject.toJSONString(resultVO));
                    if (WebConstant.RESULT_CODE_200.equals(resultVO.getCode())) {
                        dhrEaEvaluationObject.setProcessStatus(DhrEaConstant.ApprovalStatus.TERMINATION);
                        this.updateById(dhrEaEvaluationObject);
                    }
                }));
        return MessageUtils.toLocale("dhr.talent.evaluation.finishSelfSelectProcess", String.valueOf(dhrEaEvaluationObjects.size()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateScore(DhrEaObjectCalculateRequest request) {
        //查询符合算分条件的测评对象(评价中和评价完成的)
        List<DhrEaObjectCalculateResponse> allowedCalculateObjects = dhrEaEvaluationObjectMapper.findCalculateObjects(request);
        if (CollectionUtils.isEmpty(allowedCalculateObjects)) {
            return true;
        }
        //查询出各评价类别权重
        List<DhrEaEvaluationWeight> evaluationTypeWeightList = this.getEvaluationTypeWeight(request.getEaId());
        Map<String, BigDecimal> evaluationWeightMap = evaluationTypeWeightList.stream().collect(Collectors.toMap(DhrEaEvaluationWeight::getEvaluationType,
                DhrEaEvaluationWeight::getWeight, (k1, k2) -> k1));
        //查询出各考核维度权重
        Map<Long, BigDecimal> dimenWeightMap = allowedCalculateObjects.stream().collect(Collectors.toMap(DhrEaObjectCalculateResponse::getDimenId, DhrEaObjectCalculateResponse::getDimenWeight, (k1, k2) -> k1));
        //开始计算分数
        List<DhrEaObjectCalculateResultResponse> responseList = new ArrayList<>();
        Map<String, List<DhrEaObjectCalculateResponse>> allowedCalculateObjectsMap = allowedCalculateObjects.stream().collect(Collectors.groupingBy(DhrEaObjectCalculateResponse::getEmpCode));
        allowedCalculateObjectsMap.forEach((empCode, list) -> {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            DhrEaObjectCalculateResultResponse resultResponse = new DhrEaObjectCalculateResultResponse();
            //存在某一类别未答，需要重置比例
            Map<String, BigDecimal> eachEvaluationWeightMap = this.resetEvaluationWeight(evaluationWeightMap, list);
            //计算出每个题的分数
            List<DhrEaObjectQuestionScoreResponse> questionScoreResponseList = this.calculateQuestionScore(list, eachEvaluationWeightMap);
            resultResponse.setQuestionScoreResponseList(questionScoreResponseList);
            //计算每个每个人总分数,每个评价类型分数，每个维度，能力项分数
            this.calculateEachScore(questionScoreResponseList, dimenWeightMap, resultResponse);
            BeanUtil.copyProperties(list.get(0), resultResponse);
            responseList.add(resultResponse);
        });
        //保存计算结果信息
        this.saveCalculateResult(responseList, allowedCalculateObjects, evaluationTypeWeightList);
        return true;
    }

    @Override
    public ResponsePage<DhrEaObjectReportResponse> reportPageList(Page<Object> page, DhrEaObjectReportRequest request, BaseOrder order) {
        //查询权限下的活动id集合
        DhrEaActivityInfoListRequest activityInfoListRequest = new DhrEaActivityInfoListRequest();
        activityInfoListRequest.setEaStatus(DhrEaConstant.ActivityInfo.EA_STATUS_TY);
        List<Long> allowedEaIds = dhrEaActivityInfoMapper.findList(activityInfoListRequest).stream().map(DhrEaActivityInfoResponse::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(allowedEaIds)) {
            return new ResponsePage<>(page, Collections.emptyList());
        }
        request.setAllowedEaIds(allowedEaIds);
        //查询主数据组织路径信息
        if (StringUtils.isNotBlank(request.getOrgCode())) {
            request.setOrgPath(talentFeignMethodUtil.findByOrgCode(request.getOrgCode()).getOrgPath());
        }
        List<DhrEaObjectReportResponse> list = dhrEaEvaluationObjectMapper.reportPageList(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    public void exportReportByAbility(DhrEaObjectReportRequest request) {
        //查询权限下的活动id集合
        DhrEaActivityInfoListRequest activityInfoListRequest = new DhrEaActivityInfoListRequest();
        activityInfoListRequest.setEaStatus(DhrEaConstant.ActivityInfo.EA_STATUS_TY);
        List<Long> allowedEaIds = dhrEaActivityInfoMapper.findList(activityInfoListRequest).stream().map(DhrEaActivityInfoResponse::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(allowedEaIds)) {
            return;
        }
        request.setAllowedEaIds(allowedEaIds);
        //查询主数据组织路径信息
        if (StringUtils.isNotBlank(request.getOrgCode())) {
            request.setOrgPath(talentFeignMethodUtil.findByOrgCode(request.getOrgCode()).getOrgPath());
        }
        //固定表头
        List<List<String>> headerList = DynamicExcelUtil.headerList(ReportExportDTO.class);
        List<DhrEaReportAbilityScoreResponse> abilityScores = dhrEaReportAbilityScoreMapper.listForExport(request);
        //动态表头
        List<List<String>> addHeaderList = abilityScores.stream().map(a -> Lists.newArrayList(String.format("%s-%s", a.getAbilityCode(), a.getAbilityName())))
                .distinct().collect(Collectors.toList());
        headerList.addAll(addHeaderList);
        List<LinkedHashMap<String, String>> reportMapList = Lists.newArrayList();
        //固定data
        List<ReportExportDTO> reportList = dhrEaEvaluationObjectMapper.reportExport(request);
        buildTypeScore(reportList);
        //动态data
        Map<Long, Map<Long, Map<String, List<DhrEaReportAbilityScoreResponse>>>> abilityMap = abilityScores.stream().collect(
                Collectors.groupingBy(
                        DhrEaReportAbilityScoreResponse::getAbilityId, Collectors.groupingBy(
                                DhrEaReportAbilityScoreResponse::getEaId, Collectors.groupingBy(
                                        DhrEaReportAbilityScoreResponse::getEmpCode))));
        reportList.forEach(r -> {
            LinkedHashMap<String, String> map = Maps.newLinkedHashMap();
            BeanUtil.copyProperties(r, map, EA_ID);
            abilityMap.forEach((k, v) -> {
                if (v.get(Long.valueOf(r.getEaId())) != null && v.get(Long.valueOf(r.getEaId())).get(r.getEmpCode()) != null) {
                    DhrEaReportAbilityScoreResponse score = v.get(Long.valueOf(r.getEaId())).get(r.getEmpCode()).get(0);
                    map.put(score.getAbilityName(), score.getComprehensiveScore().toString());
                }
            });
            reportMapList.add(map);
        });
        List<List<String>> excelList = reportMapList.stream().map(m -> m.values().stream().collect(Collectors.toList())).collect(Collectors.toList());
        ExcelSheetData excelSheetData = new ExcelSheetData(null, excelList);
        String fileName = MessageUtil.getText("export.name.dhr.evaluation.report.ability") + DateUtil.now() + ".xlsx";
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(fileName, CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), headerList);
    }

    /**
     * 类别的成绩为空则用‘-’替换
     *
     * @param reportList
     */
    private void buildTypeScore(List<ReportExportDTO> reportList) {
        reportList.forEach(reportExportDTO -> {
            if (reportExportDTO.getSuperiorScore() == null) {
                reportExportDTO.setSuperiorScore("-");
            }
            if (reportExportDTO.getSameLevelScore() == null) {
                reportExportDTO.setSameLevelScore("-");
            }
            if (reportExportDTO.getLowerLevelScore() == null) {
                reportExportDTO.setLowerLevelScore("-");
            }
            if (reportExportDTO.getMyselfScore() == null) {
                reportExportDTO.setMyselfScore("-");
            }
            if (reportExportDTO.getOtherScore() == null) {
                reportExportDTO.setOtherScore("-");
            }
        });
    }

    @Override
    public void exportReportByQuestion(DhrEaObjectReportRequest request) {
        //查询权限下的活动id集合
        DhrEaActivityInfoListRequest activityInfoListRequest = new DhrEaActivityInfoListRequest();
        activityInfoListRequest.setEaStatus(DhrEaConstant.ActivityInfo.EA_STATUS_TY);
        List<Long> allowedEaIds = dhrEaActivityInfoMapper.findList(activityInfoListRequest).stream().map(DhrEaActivityInfoResponse::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(allowedEaIds)) {
            return;
        }
        request.setAllowedEaIds(allowedEaIds);
        //查询主数据组织路径信息
        if (StringUtils.isNotBlank(request.getOrgCode())) {
            request.setOrgPath(talentFeignMethodUtil.findByOrgCode(request.getOrgCode()).getOrgPath());
        }
        //固定表头
        List<List<String>> headerList = DynamicExcelUtil.headerList(ReportExportDTO.class);
        List<DhrEaReportQuestionScoreResponse> questionScores = dhrEaReportQuestionScoreMapper.listForExport(request);
        //动态表头
        List<List<String>> addHeaderList = questionScores.stream().map(a -> Lists.newArrayList(String.format("%s-%s", a.getTqCode(), a.getTqStem())))
                .distinct().collect(Collectors.toList());
        headerList.addAll(addHeaderList);
        List<LinkedHashMap<String, String>> reportMapList = Lists.newArrayList();
        //固定data
        List<ReportExportDTO> reportList = dhrEaEvaluationObjectMapper.reportExport(request);
        buildTypeScore(reportList);
        //动态data
        Map<Long, Map<Long, Map<String, List<DhrEaReportQuestionScoreResponse>>>> abilityMap = questionScores.stream().collect(
                Collectors.groupingBy(
                        DhrEaReportQuestionScoreResponse::getTqId, Collectors.groupingBy(
                                DhrEaReportQuestionScoreResponse::getEaId, Collectors.groupingBy(
                                        DhrEaReportQuestionScoreResponse::getEmpCode))));
        reportList.forEach(r -> {
            LinkedHashMap<String, String> map = Maps.newLinkedHashMap();
            BeanUtil.copyProperties(r, map, EA_ID);
            abilityMap.forEach((k, v) -> {
                if (v.get(Long.valueOf(r.getEaId())) != null && v.get(Long.valueOf(r.getEaId())).get(r.getEmpCode()) != null) {
                    DhrEaReportQuestionScoreResponse score = v.get(Long.valueOf(r.getEaId())).get(r.getEmpCode()).get(0);
                    map.put(score.getTqStem(), score.getComprehensiveScore().toString());
                }
            });
            reportMapList.add(map);
        });
        List<List<String>> excelList = reportMapList.stream().map(m -> m.values().stream().collect(Collectors.toList())).collect(Collectors.toList());
        ExcelSheetData excelSheetData = new ExcelSheetData(null, excelList);
        String fileName = MessageUtil.getText("export.name.dhr.evaluation.report.question") + DateUtil.now() + ".xlsx";
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(fileName, CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), headerList);

    }

    @Override
    public ResponsePage<DhrEaPersonalReportListResponse> personalReportEmpList(Page<DhrEaEvaluationObject> page, DhrEaPersonalReportListRequest request) {
        List<DhrEaPersonalReportListResponse> list = dhrEaEvaluationObjectMapper.personalReportEmpList(page, request);
        return new ResponsePage<>(page, list);
    }

    @Override
    public Boolean canEdit(Long eaId) {
        DhrEaActivityInfo activityInfo = dhrEaActivityInfoMapper.selectById(eaId);
        if (DhrEaConstant.ActivityInfo.EA_STATUS_CG.equals(activityInfo.getEaStatus())) {
            return true;
        }
        DhrEaEvaluationPathResponse pathResponse = dhrEaEvaluationPathService.findByEaId(eaId);
        //未开启自助选人
        if (DhrEaConstant.NO.equals(pathResponse.getEnabled())) {
            return true;
        }
        LambdaQueryWrapper<DhrEaEvaluationObject> wrapper = new LambdaQueryWrapper<DhrEaEvaluationObject>()
                .eq(DhrEaEvaluationObject::getEaId, eaId)
                .eq(DhrEaEvaluationObject::getProcessStatus, DhrEaConstant.ApprovalStatus.TERMINATION);
        //存在流程终止状态
        if (CollUtil.isNotEmpty(list(wrapper))) {
            return true;
        }
        wrapper.clear();
        wrapper.eq(DhrEaEvaluationObject::getEaId, eaId);
        long allTotal = count(wrapper);
        wrapper.eq(DhrEaEvaluationObject::getCurrentStage, DhrEaConstant.ApprovalCurrentStage.FINISH);
        long finishTotal = count(wrapper);
        //所有审批阶段都是已完成阶段
        return allTotal == finishTotal;
    }

    /**
     * 保存计算结果信息
     *
     * @param responseList
     * @param allowedCalculateObjects
     * @param evaluationTypeWeightList
     */
    private void saveCalculateResult(List<DhrEaObjectCalculateResultResponse> responseList, List<DhrEaObjectCalculateResponse> allowedCalculateObjects,
                                     List<DhrEaEvaluationWeight> evaluationTypeWeightList) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }
        //能力项名称和维度map，用于最终数据的转译
        Map<Long, DhrEaObjectCalculateResponse> abilityMap = allowedCalculateObjects.stream().collect(Collectors.toMap(DhrEaObjectCalculateResponse::getAbilityId, k -> k, (k1, k2) -> k1));
        Map<Long, DhrEaObjectCalculateResponse> dimenMap = allowedCalculateObjects.stream().collect(Collectors.toMap(DhrEaObjectCalculateResponse::getDimenId, k -> k, (k1, k2) -> k1));
        //组装数据,把没有开启的评价类型的分数设置为空
        responseList.forEach(resultResponse -> {
            //设置未开启的评价类型的分数为null
            evaluationTypeWeightList.forEach(evaluationWeight -> {
                if (DhrEaConstant.ENABLE.equals(evaluationWeight.getEnabled())) {
                    return;
                }
                this.setEnableEvaluationTypeScore(evaluationWeight.getEvaluationType(), Collections.singletonList(resultResponse));
                this.setEnableEvaluationTypeScore(evaluationWeight.getEvaluationType(), resultResponse.getDimenScoreResponseList());
                this.setEnableEvaluationTypeScore(evaluationWeight.getEvaluationType(), resultResponse.getAbilityScoreResponseList());
                this.setEnableEvaluationTypeScore(evaluationWeight.getEvaluationType(), resultResponse.getLevelScoreResponseList());
                this.setEnableEvaluationTypeScore(evaluationWeight.getEvaluationType(), resultResponse.getQuestionScoreResponseList());
            });
            //设置能力项定义和名称
            resultResponse.getAbilityScoreResponseList().forEach(abilityScore -> {
                DhrEaObjectCalculateResponse dhrEaObjectCalculateResponse = abilityMap.get(abilityScore.getAbilityId());
                if (dhrEaObjectCalculateResponse == null) {
                    return;
                }
                BeanUtil.copyProperties(dhrEaObjectCalculateResponse, abilityScore);
            });
            //设置维度名称
            resultResponse.getDimenScoreResponseList().forEach(dimenScore -> {
                DhrEaObjectCalculateResponse dhrEaObjectCalculateResponse = dimenMap.get(dimenScore.getDimenId());
                if (dhrEaObjectCalculateResponse == null) {
                    return;
                }
                dimenScore.setDimenName(dhrEaObjectCalculateResponse.getDimenName());
            });
        });
        //保存统计信息分数
        this.saveEvaluationStatistics(responseList);
        //保存报表相关信息分数
        //报表能力项分数信息
        dhrEaReportAbilityScoreService.saveOrUpdateBatch(responseList);
        //报表维度分数信息
        dhrEaReportDimenScoreService.saveOrUpdateBatch(responseList);
        //报表试题分数信息
        dhrEaReportQuestionScoreService.saveOrUpdateBatch(responseList);
        //报表总分数信息
        dhrEaReportTotalScoreService.saveOrUpdateBatch(responseList);
    }

    /**
     * 设置未开启评价类型的分数
     *
     * @param evaluationType
     * @param list
     * @param <T>
     */
    private <T extends DhrEaObjectTypeScoreResponse> void setEnableEvaluationTypeScore(String evaluationType, List<T> list) {
        switch (evaluationType) {
            case DhrEaConstant.EvaluationType.SJ_TYPE:
                list.forEach(typeScoreResponse -> {
                    typeScoreResponse.setSuperiorScore(null);
                    typeScoreResponse.setSuperiorWeightScore(null);
                });
                break;
            case DhrEaConstant.EvaluationType.PJ_TYPE:
                list.forEach(typeScoreResponse -> {
                    typeScoreResponse.setSameLevelScore(null);
                    typeScoreResponse.setSameLevelWeightScore(null);
                });
                break;
            case DhrEaConstant.EvaluationType.XJ_TYPE:
                list.forEach(typeScoreResponse -> {
                    typeScoreResponse.setLowerLevelScore(null);
                    typeScoreResponse.setLowerLevelWeightScore(null);
                });
                break;
            case DhrEaConstant.EvaluationType.QT_TYPE:
                list.forEach(typeScoreResponse -> {
                    typeScoreResponse.setOtherScore(null);
                    typeScoreResponse.setOtherWeightScore(null);
                });
                break;
            case DhrEaConstant.EvaluationType.BR_TYPE:
                list.forEach(typeScoreResponse -> {
                    typeScoreResponse.setMyselfScore(null);
                    typeScoreResponse.setMyselfWeightScore(null);
                });
                break;
            default:
        }
    }

    /**
     * 保存统计信息
     *
     * @param responseList
     */
    private void saveEvaluationStatistics(List<DhrEaObjectCalculateResultResponse> responseList) {
        List<String> empCodes = responseList.stream().map(DhrEaObjectCalculateResultResponse::getEmpCode).collect(Collectors.toList());
        Map<String, DhrEaObjectCalculateResultResponse> resultMap = responseList.stream().collect(Collectors.toMap(DhrEaObjectCalculateResultResponse::getEmpCode, k -> k, (k1, k2) -> k1));
        LambdaQueryWrapper<DhrEaEvaluationStatistics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DhrEaEvaluationStatistics::getEvaluationObject, empCodes);
        List<DhrEaEvaluationStatistics> evaluationStatistics = dhrEaEvaluationStatisticsMapper.selectList(queryWrapper);
        Map<String, List<DhrEaEvaluationStatistics>> map = evaluationStatistics.stream().collect(Collectors.groupingBy(DhrEaEvaluationStatistics::getEvaluationObject));
        map.forEach((empCode, list) -> {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            DhrEaObjectCalculateResultResponse response = resultMap.get(empCode);
            if (response == null) {
                return;
            }
            list.forEach(dhrEaEvaluationStatistics -> {
                switch (dhrEaEvaluationStatistics.getEvaluationType()) {
                    case DhrEaConstant.EvaluationType.SJ_TYPE:
                        dhrEaEvaluationStatistics.setEvaluationScore(response.getSuperiorScore());
                        break;
                    case DhrEaConstant.EvaluationType.PJ_TYPE:
                        dhrEaEvaluationStatistics.setEvaluationScore(response.getSameLevelScore());
                        break;
                    case DhrEaConstant.EvaluationType.XJ_TYPE:
                        dhrEaEvaluationStatistics.setEvaluationScore(response.getLowerLevelScore());
                        break;
                    case DhrEaConstant.EvaluationType.QT_TYPE:
                        dhrEaEvaluationStatistics.setEvaluationScore(response.getOtherScore());
                        break;
                    case DhrEaConstant.EvaluationType.BR_TYPE:
                        dhrEaEvaluationStatistics.setEvaluationScore(response.getMyselfScore());
                        break;
                    default:
                }
            });
        });
        dhrEaEvaluationStatisticsService.saveOrUpdateBatch(evaluationStatistics);
    }

    /**
     * 重置评价类别权重
     *
     * @param evaluationWeightMap
     * @param list
     */
    private Map<String, BigDecimal> resetEvaluationWeight(Map<String, BigDecimal> evaluationWeightMap, List<DhrEaObjectCalculateResponse> list) {
        Map<String, BigDecimal> eachEvaluationWeightMap = new HashMap<>(evaluationWeightMap);
        List<String> evaluationTypes = list.stream().map(DhrEaObjectCalculateResponse::getEvaluationType).distinct().collect(Collectors.toList());
        BigDecimal totalWeight = evaluationTypes.stream().map(evaluationWeightMap::get).reduce(BigDecimal.ZERO, BigDecimal::add);
        //重新计算权重
        for (Map.Entry<String, BigDecimal> entry : eachEvaluationWeightMap.entrySet()) {
            if (evaluationTypes.contains(entry.getKey())) {
                BigDecimal weight = totalWeight.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : entry.getValue().divide(totalWeight, 10, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                entry.setValue(weight);
            } else {
                entry.setValue(BigDecimal.ZERO);
            }
        }
        return eachEvaluationWeightMap;
    }

    /**
     * 查询该活动评价类型权重
     *
     * @param eaId
     * @return
     */
    private List<DhrEaEvaluationWeight> getEvaluationTypeWeight(Long eaId) {
        LambdaQueryWrapper<DhrEaEvaluationWeight> weightQueryWrapper = new LambdaQueryWrapper<>();
        weightQueryWrapper.eq(DhrEaEvaluationWeight::getEaId, eaId);
        return dhrEaEvaluationWeightMapper.selectList(weightQueryWrapper);
    }

    /**
     * 计算每道题分数
     *
     * @param list
     * @param evaluationWeightMap
     * @return
     */
    private List<DhrEaObjectQuestionScoreResponse> calculateQuestionScore(List<DhrEaObjectCalculateResponse> list, Map<String, BigDecimal> evaluationWeightMap) {
        //按题分类,计算出每个题的分数，每道题的总分数 = 每道题的各个类型平均分 * 对应权重 之和
        Map<Long, List<DhrEaObjectCalculateResponse>> listByTqIdMap = list.stream().collect(Collectors.groupingBy(DhrEaObjectCalculateResponse::getTqId));
        //每道题的分数结果保存对象
        List<DhrEaObjectQuestionScoreResponse> questionScoreList = new ArrayList<>();
        listByTqIdMap.forEach((tqId, eachQuestionEvaluationTypeList) -> {
            if (CollectionUtils.isEmpty(eachQuestionEvaluationTypeList)) {
                return;
            }
            Map<String, List<DhrEaObjectCalculateResponse>> eachQuestionByEvaluationTypeMap = eachQuestionEvaluationTypeList.stream().collect(Collectors.groupingBy(DhrEaObjectCalculateResponse::getEvaluationType));
            DhrEaObjectQuestionScoreResponse questionScore = new DhrEaObjectQuestionScoreResponse();
            eachQuestionByEvaluationTypeMap.forEach((evaluationType, questionList) -> {
                //计算每道题的分数
                this.calculateEachQuestionScore(evaluationType, evaluationWeightMap, questionList, questionScore);
            });
            questionScoreList.add(questionScore);
        });
        questionScoreList.forEach(response -> {
            BigDecimal totalScore = response.getSuperiorWeightScore().add(response.getOtherWeightScore()).add(response.getLowerLevelWeightScore())
                    .add(response.getSameLevelWeightScore());
            response.setTotalScore(totalScore);
        });
        return questionScoreList;
    }

    /**
     * 计算每个人总分数
     *
     * @param list
     * @param dimenWeightMap
     * @param resultResponse
     */
    private void calculateEachScore(List<DhrEaObjectQuestionScoreResponse> list, Map<Long, BigDecimal> dimenWeightMap, DhrEaObjectCalculateResultResponse resultResponse) {
        //按照考核维度分类
        Map<Long, List<DhrEaObjectQuestionScoreResponse>> listByDimenIdMap = list.stream().collect(Collectors.groupingBy(
                DhrEaObjectQuestionScoreResponse::getDimenId));
        List<DhrEaObjectDimenScoreResponse> dimenScoreResponseList = new ArrayList<>();
        //遍历每个维度，返回每个维度的能力项分数
        for (Map.Entry<Long, List<DhrEaObjectQuestionScoreResponse>> mapEntry : listByDimenIdMap.entrySet()) {
            //计算每个维度分数
            DhrEaObjectDimenScoreResponse dimenScore = this.calculateEachDimenScore(mapEntry.getValue(), mapEntry.getKey(), resultResponse);
            dimenScoreResponseList.add(dimenScore);
        }
        resultResponse.setDimenScoreResponseList(dimenScoreResponseList);
        //总分 = 每个加权考察维度分数之和
        resultResponse.setTotalScore(dimenScoreResponseList.stream().map(dimenScore -> dimenScore.getTotalScore().multiply(dimenWeightMap.get(dimenScore.getDimenId())).multiply(BigDecimal.valueOf(0.01)))
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        resultResponse.setSuperiorScore(dimenScoreResponseList.stream().map(dimenScore -> dimenScore.getSuperiorScore().multiply(dimenWeightMap.get(dimenScore.getDimenId())).multiply(BigDecimal.valueOf(0.01)))
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        resultResponse.setSameLevelScore(dimenScoreResponseList.stream().map(dimenScore -> dimenScore.getSameLevelScore().multiply(dimenWeightMap.get(dimenScore.getDimenId())).multiply(BigDecimal.valueOf(0.01)))
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        resultResponse.setLowerLevelScore(dimenScoreResponseList.stream().map(dimenScore -> dimenScore.getLowerLevelScore().multiply(dimenWeightMap.get(dimenScore.getDimenId())).multiply(BigDecimal.valueOf(0.01)))
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        resultResponse.setOtherScore(dimenScoreResponseList.stream().map(dimenScore -> dimenScore.getOtherScore().multiply(dimenWeightMap.get(dimenScore.getDimenId())).multiply(BigDecimal.valueOf(0.01)))
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        resultResponse.setMyselfScore(dimenScoreResponseList.stream().map(dimenScore -> dimenScore.getMyselfScore().multiply(dimenWeightMap.get(dimenScore.getDimenId())).multiply(BigDecimal.valueOf(0.01)))
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
    }

    /**
     * 考察维度分数 = 该维度下所有能力项分数平均数
     *
     * @param list
     * @param dimenId
     * @param resultResponse
     * @return
     */
    private DhrEaObjectDimenScoreResponse calculateEachDimenScore(List<DhrEaObjectQuestionScoreResponse> list, Long dimenId, DhrEaObjectCalculateResultResponse resultResponse) {
        DhrEaObjectDimenScoreResponse dimenScoreResponse = new DhrEaObjectDimenScoreResponse();
        dimenScoreResponse.setDimenId(dimenId);
        //按照能力项分类
        Map<Long, List<DhrEaObjectQuestionScoreResponse>> listByAbilityIdMap = list.stream().collect(Collectors.groupingBy(DhrEaObjectQuestionScoreResponse::getAbilityId));
        //计算每个能力项分数
        List<DhrEaObjectAbilityScoreResponse> abilityScoreResponseList = new ArrayList<>();
        for (Map.Entry<Long, List<DhrEaObjectQuestionScoreResponse>> e : listByAbilityIdMap.entrySet()) {
            DhrEaObjectAbilityScoreResponse abilityScore = this.calculateEachAbilityScore(e.getValue(), e.getKey(), dimenId, resultResponse);
            abilityScoreResponseList.add(abilityScore);
        }
        //计算各类型分数
        this.calculateScore(dimenScoreResponse, new ArrayList<>(abilityScoreResponseList));
        resultResponse.getAbilityScoreResponseList().addAll(abilityScoreResponseList);
        return dimenScoreResponse;
    }

    /**
     * 能力项分数 = 该能力项下所有能力层级分数平均数
     *
     * @param list
     * @param abilityId
     * @param dimenId
     * @param resultResponse
     * @return
     */
    private DhrEaObjectAbilityScoreResponse calculateEachAbilityScore(List<DhrEaObjectQuestionScoreResponse> list, Long abilityId, Long dimenId,
                                                                      DhrEaObjectCalculateResultResponse resultResponse) {
        DhrEaObjectAbilityScoreResponse abilityScoreResponse = new DhrEaObjectAbilityScoreResponse();
        abilityScoreResponse.setAbilityId(abilityId);
        abilityScoreResponse.setDimenId(dimenId);
        List<DhrEaObjectAbilityLevelScoreResponse> levelScoreResponseList = new ArrayList<>();
        //按能力项层级分类
        Map<Long, List<DhrEaObjectQuestionScoreResponse>> listByAbilityLevelIdMap = list.stream()
                .collect(Collectors.groupingBy(DhrEaObjectQuestionScoreResponse::getAbilityLevelId));
        for (Map.Entry<Long, List<DhrEaObjectQuestionScoreResponse>> entry : listByAbilityLevelIdMap.entrySet()) {
            //计算每个能力项层级分数
            DhrEaObjectAbilityLevelScoreResponse abilityLevelScore = this.calculateEachAbilityLevelScore(entry.getValue(), entry.getKey());
            levelScoreResponseList.add(abilityLevelScore);
        }
        //计算各类型分数
        this.calculateScore(abilityScoreResponse, new ArrayList<>(levelScoreResponseList));
        resultResponse.getLevelScoreResponseList().addAll(levelScoreResponseList);
        return abilityScoreResponse;
    }

    /**
     * 每个能力项层级分数 = 每个能力项下面所有试题分数平均数
     *
     * @param list
     * @param abilityLevelId
     * @return
     */
    private DhrEaObjectAbilityLevelScoreResponse calculateEachAbilityLevelScore(List<DhrEaObjectQuestionScoreResponse> list, Long abilityLevelId) {
        //能力项层级分数实体
        DhrEaObjectAbilityLevelScoreResponse levelScoreResponse = new DhrEaObjectAbilityLevelScoreResponse();
        if (CollectionUtils.isEmpty(list)) {
            return levelScoreResponse;
        }
        levelScoreResponse.setAbilityLevelId(abilityLevelId);
        //计算各类型分数
        this.calculateScore(levelScoreResponse, new ArrayList<>(list));
        return levelScoreResponse;
    }

    /**
     * 计算各类型分数
     *
     * @param t
     * @param list
     * @param <T>
     */
    private <T extends DhrEaObjectTypeScoreResponse> void calculateScore(T t, List<T> list) {
        //总分数
        t.setTotalScore(list.stream().map(T::getTotalScore).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 10, RoundingMode.HALF_UP));
        //上级分数
        t.setSuperiorScore(list.stream().map(T::getSuperiorScore).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 10, RoundingMode.HALF_UP));
        //本人分数
        t.setMyselfScore(list.stream().map(T::getMyselfScore).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 10, RoundingMode.HALF_UP));
        //同级分数
        t.setSameLevelScore(list.stream().map(T::getSameLevelScore).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 10, RoundingMode.HALF_UP));
        //其他分数
        t.setOtherScore(list.stream().map(T::getOtherScore).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 10, RoundingMode.HALF_UP));
        //下级分数
        t.setLowerLevelScore(list.stream().map(T::getLowerLevelScore).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(list.size()), 10, RoundingMode.HALF_UP));
    }

    /**
     * 计算每道题的分数
     *
     * @param evaluationType
     * @param evaluationWeightMap
     * @param questionList
     * @param questionScore
     * @return
     */
    private void calculateEachQuestionScore(String evaluationType, Map<String, BigDecimal> evaluationWeightMap,
                                            List<DhrEaObjectCalculateResponse> questionList, DhrEaObjectQuestionScoreResponse questionScore) {
        BeanUtil.copyProperties(questionList.get(0), questionScore);
        //计算每个类型平均分数
        //总分数 = 已答题试题分数之和
        BigDecimal eachEvaluationTypeTotalScore = questionList.stream().map(DhrEaObjectCalculateResponse::getAnswerScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        //条数 = 非不了解项条数
        long size = questionList.stream().filter(response -> BigDecimal.ZERO.compareTo(response.getAnswerScore()) != 0).count();
        BigDecimal eachEvaluationTypeAvgScore = size == 0 ? BigDecimal.ZERO : eachEvaluationTypeTotalScore.divide(BigDecimal.valueOf(size), 10, RoundingMode.HALF_UP);
        BigDecimal eachEvaluationTypeScore = eachEvaluationTypeAvgScore.multiply(evaluationWeightMap.get(evaluationType)).multiply(BigDecimal.valueOf(0.01));
        switch (evaluationType) {
            case DhrEaConstant.EvaluationType.SJ_TYPE:
                questionScore.setSuperiorScore(eachEvaluationTypeAvgScore);
                questionScore.setSuperiorWeightScore(eachEvaluationTypeScore);
                break;
            case DhrEaConstant.EvaluationType.PJ_TYPE:
                questionScore.setSameLevelScore(eachEvaluationTypeAvgScore);
                questionScore.setSameLevelWeightScore(eachEvaluationTypeScore);
                break;
            case DhrEaConstant.EvaluationType.XJ_TYPE:
                questionScore.setLowerLevelScore(eachEvaluationTypeAvgScore);
                questionScore.setLowerLevelWeightScore(eachEvaluationTypeScore);
                break;
            case DhrEaConstant.EvaluationType.QT_TYPE:
                questionScore.setOtherScore(eachEvaluationTypeAvgScore);
                questionScore.setOtherWeightScore(eachEvaluationTypeScore);
                break;
            case DhrEaConstant.EvaluationType.BR_TYPE:
                questionScore.setMyselfScore(eachEvaluationTypeAvgScore);
                questionScore.setMyselfWeightScore(eachEvaluationTypeScore);
                break;
            default:
        }
    }


}

