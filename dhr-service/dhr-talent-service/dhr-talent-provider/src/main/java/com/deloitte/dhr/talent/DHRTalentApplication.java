package com.deloitte.dhr.talent;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableScheduling
@EnableFeignClients(basePackages = {"com.deloitte.dhr.mda.module", "com.deloitte.bpm.provider", "com.deloitte.dhr.utility", "com.deloitte.dhr.ssc"})
@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = {"com.deloitte.dhr.common", "com.deloitte.dhr.talent", "com.deloitte.dhr.mda", "com.deloitte.bpm", "com.deloitte.dhr.utility", "com.deloitte.dhr.ssc", "com.deloitte.dhr.excel.parser"})
@EnableRetry
public class DHRTalentApplication {
    public static void main(String[] args) {
        SpringApplication.run(DHRTalentApplication.class, args);
    }
}
