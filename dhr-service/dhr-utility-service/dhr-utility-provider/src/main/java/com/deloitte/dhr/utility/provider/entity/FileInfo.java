package com.deloitte.dhr.utility.provider.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "T_FILE_INFO")
public class FileInfo implements Serializable {
    private static final long serialVersionUID = -8841547525082597513L;
    @Id
    @Column(name = "ID")
    private Long id;

    @Column(name = "ATTACH_NAME")
    private String attachName;

    @Column(name = "ATTACH_TYPE")
    private String attachType;

    @Column(name = "FILE_GROUP_ID")
    private String fileGroupId;

    @Column(name = "KEY")
    private String key;

    @Column(name = "FILE_SIZE")
    private String fileSize;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "CREATE_USER")
    private String createUser;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * @return ID
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return ATTACH_NAME
     */
    public String getAttachName() {
        return attachName;
    }

    /**
     * @param attachName
     */
    public void setAttachName(String attachName) {
        this.attachName = attachName;
    }

    /**
     * @return ATTACH_TYPE
     */
    public String getAttachType() {
        return attachType;
    }

    /**
     * @param attachType
     */
    public void setAttachType(String attachType) {
        this.attachType = attachType;
    }

    /**
     * @return FILE_GROUP_ID
     */
    public String getFileGroupId() {
        return fileGroupId;
    }

    /**
     * @param fileGroupId
     */
    public void setFileGroupId(String fileGroupId) {
        this.fileGroupId = fileGroupId;
    }

    /**
     * @return KEY
     */
    public String getKey() {
        return key;
    }

    /**
     * @param key
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * @return FILE_SIZE
     */
    public String getFileSize() {
        return fileSize;
    }

    /**
     * @param fileSize
     */
    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    /**
     * @return CREATED_DATE
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * @param createdDate
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * @return CREATE_USER
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * @param createUser
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * @return UPDATE_DATE
     */
    public Date getUpdatedDate() {
        return updatedDate;
    }

    /**
     * @param updatedDate
     */
    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    /**
     * @return UPDATE_USER
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * @param updateUser
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
}