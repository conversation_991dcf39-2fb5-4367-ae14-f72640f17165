# Spring Boot 3.x 迁移修复计划

## 🎯 目标
将DHR项目从Spring Boot 2.7.x成功迁移到Spring Boot 3.2.4，解决所有兼容性问题。

## 📊 问题分析

### 1. Jakarta EE 迁移问题
- **javax.mail** → **jakarta.mail**
- **javax.servlet** → **jakarta.servlet**
- **javax.persistence** → **jakarta.persistence**

### 2. 依赖兼容性问题
- MyBatis Plus 3.5.9 兼容性
- FastDFS 客户端API变化
- Aspose Words 依赖缺失
- Spring Cloud Alibaba 组件兼容性

### 3. 配置变化
- Nacos 配置方式
- Bean 定义方式
- 安全配置变化

## 🛠️ 修复步骤

### 阶段1: 依赖更新
1. **更新pom.xml依赖版本**
   ```xml
   <!-- 确保所有依赖都兼容Spring Boot 3.x -->
   <mybatis-plus.version>3.5.9</mybatis-plus.version>
   <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
   ```

2. **添加缺失依赖**
   ```xml
   <!-- FastDFS 客户端 -->
   <dependency>
       <groupId>org.csource</groupId>
       <artifactId>fastdfs-client-java</artifactId>
       <version>1.29-SNAPSHOT</version>
   </dependency>
   ```

### 阶段2: 代码迁移
1. **Jakarta EE 包名替换**
   - 全局替换 `javax.mail` → `jakarta.mail`
   - 全局替换 `javax.servlet` → `jakarta.servlet`
   - 全局替换 `javax.persistence` → `jakarta.persistence`

2. **API 适配**
   - 更新 FastDFS 客户端API调用
   - 修复 MyBatis Plus Model 类使用
   - 更新邮件发送相关代码

### 阶段3: 配置修复
1. **Nacos 配置**
   - 更新 application.yml 中的 Nacos 配置
   - 修复 spring.config.import 配置

2. **Bean 配置**
   - 修复 @Configuration 类
   - 更新 Bean 定义方式

### 阶段4: 测试验证
1. **编译测试**
   - 确保所有模块编译通过
   - 解决编译错误

2. **启动测试**
   - 逐个启动服务
   - 验证服务间通信

## 🚀 快速启动方案

### 临时解决方案：版本回退
如果需要快速启动服务，建议临时回退版本：

```xml
<spring-boot.version>2.7.18</spring-boot.version>
<spring-cloud.version>2021.0.8</spring-cloud.version>
<spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
```

### 优先修复的服务
1. **dhr-xxl-job-admin-service** - 定时任务服务
2. **dhr-oauth-service** - 认证服务
3. **dhr-gateway-service** - 网关服务

## 📝 具体修复任务

### 任务1: 修复邮件服务
文件: `dhr-xxl-job-admin-service/src/main/java/com/deloitte/dhr/xxl/job/admin/core/alarm/impl/EmailJobAlarm.java`
```java
// 替换
import javax.mail.internet.MimeMessage;
// 为
import jakarta.mail.internet.MimeMessage;
```

### 任务2: 修复FastDFS
文件: `dhr-utility-provider/src/main/java/com/deloitte/dhr/utility/provider/config/FastDFSConnectionPool.java`
- 更新FastDFS客户端依赖
- 修改API调用方式

### 任务3: 修复MyBatis Plus
文件: `dhr-collection-api/src/main/java/com/deloitte/dhr/collection/model/CollectAction.java`
- 检查Model类的正确使用方式
- 更新分页配置

## ⏰ 时间估算
- **完整迁移**: 2-3天
- **版本回退**: 半天
- **部分修复**: 1-2天

## 🎯 建议
基于当前情况，建议采用**版本回退方案**快速解决启动问题，然后在后续版本中逐步完成Spring Boot 3.x迁移。
